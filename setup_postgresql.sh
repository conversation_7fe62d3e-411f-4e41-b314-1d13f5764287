#!/bin/bash

# PostgreSQL 10 Setup Script for Kylas Customer Success Portal
# This script installs and configures PostgreSQL 10 for development

set -e

echo "🐘 PostgreSQL 10 Setup for Kylas Customer Success Portal"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
else
    print_error "Unsupported operating system: $OSTYPE"
    exit 1
fi

echo "Detected OS: $OS"

# Install PostgreSQL 10
install_postgresql() {
    if command -v psql &> /dev/null; then
        PG_VERSION=$(psql --version | awk '{print $3}' | sed 's/\..*//')
        if [[ "$PG_VERSION" == "10" ]]; then
            print_status "PostgreSQL 10 is already installed"
            return 0
        else
            print_warning "PostgreSQL $PG_VERSION is installed, but we need version 10"
        fi
    fi

    echo "Installing PostgreSQL 10..."
    
    if [[ "$OS" == "linux" ]]; then
        # Ubuntu/Debian
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y wget ca-certificates
            wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
            echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
            sudo apt-get update
            sudo apt-get install -y postgresql-10 postgresql-client-10 postgresql-contrib-10 libpq-dev
        # CentOS/RHEL/Fedora
        elif command -v yum &> /dev/null; then
            sudo yum install -y https://download.postgresql.org/pub/repos/yum/reporpms/EL-7-x86_64/pgdg-redhat-repo-latest.noarch.rpm
            sudo yum install -y postgresql10-server postgresql10 postgresql10-contrib postgresql10-devel
            sudo /usr/pgsql-10/bin/postgresql-10-setup initdb
            sudo systemctl enable postgresql-10
            sudo systemctl start postgresql-10
        fi
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew install postgresql@10
            brew services start postgresql@10
        else
            print_error "Homebrew is required for macOS installation"
            exit 1
        fi
    fi
    
    print_status "PostgreSQL 10 installed successfully"
}

# Configure PostgreSQL
configure_postgresql() {
    echo "Configuring PostgreSQL..."
    
    # Start PostgreSQL service
    if [[ "$OS" == "linux" ]]; then
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    fi
    
    # Create databases and user
    sudo -u postgres psql -c "CREATE USER postgres WITH SUPERUSER PASSWORD 'postgres';" 2>/dev/null || true
    sudo -u postgres createdb kylas_customer_success_development 2>/dev/null || true
    sudo -u postgres createdb kylas_customer_success_test 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE kylas_customer_success_development TO postgres;" 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE kylas_customer_success_test TO postgres;" 2>/dev/null || true
    
    print_status "PostgreSQL configured successfully"
}

# Test connection
test_connection() {
    echo "Testing PostgreSQL connection..."
    
    export PGPASSWORD=postgres
    if psql -h localhost -U postgres -d kylas_customer_success_development -c "SELECT version();" &> /dev/null; then
        print_status "PostgreSQL connection test successful"
    else
        print_error "PostgreSQL connection test failed"
        exit 1
    fi
}

# Main execution
main() {
    install_postgresql
    configure_postgresql
    test_connection
    
    echo ""
    echo "🎉 PostgreSQL 10 setup completed successfully!"
    echo ""
    echo "📋 Database Configuration:"
    echo "   Host: localhost"
    echo "   Port: 5432"
    echo "   Username: postgres"
    echo "   Password: postgres"
    echo "   Development DB: kylas_customer_success_development"
    echo "   Test DB: kylas_customer_success_test"
    echo ""
    echo "🚀 Next steps:"
    echo "   1. Run: bundle install"
    echo "   2. Run: rails db:create (if not already created)"
    echo "   3. Run: rails db:migrate"
    echo "   4. Run: rails db:seed"
    echo "   5. Run: rake usage_reports:demo_data"
    echo ""
    echo "💡 Environment variables (optional):"
    echo "   export DATABASE_USERNAME=postgres"
    echo "   export DATABASE_PASSWORD=postgres"
    echo "   export DATABASE_HOST=localhost"
    echo "   export DATABASE_PORT=5432"
}

# Run main function
main
