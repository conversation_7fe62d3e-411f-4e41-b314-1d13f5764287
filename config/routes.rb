Rails.application.routes.draw do
  devise_for :users, skip: 'registration'
  resources :users, only: %i[new create update destroy edit index] do
    post '/resend-invitation', to: 'users#resend_invitation', on: :member
    get '/list.json', to: 'users#list', on: :collection, as: :list
    patch :activate, on: :member
  end
  resources :account_details, path: 'account-details', only: %i[index show update] do
    member do
      get '/plan-details', to: 'plan_details#show'
      post '/plan-details', to: 'plan_details#update'

      get '/rgs-inputs', to: 'rgs_inputs#show'
      post '/rgs-inputs', to: 'rgs_inputs#update'

      get '/customer-asks', to: 'customer_asks#show'
      post '/customer-asks/remove', to: 'customer_asks#destroy'
      post '/customer-asks/edit', to: 'customer_asks#update'
      post '/customer-asks/add', to: 'customer_asks#create'

      get '/usage-reports', to: 'usage_reports#show'
      get '/usage-reports/data', to: 'usage_reports#data'
      get '/usage-reports/export', to: 'usage_reports#export'
    end
  end
  root 'account_details#index'
end
