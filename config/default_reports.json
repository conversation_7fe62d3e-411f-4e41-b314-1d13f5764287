{"reports": [{"name": "Daily Active Users Overview", "description": "Track daily active users across all tenants with login patterns and engagement metrics", "category": "user_activity", "metrics": ["logged_in_users_count", "dau_true", "dau_false"], "chart_type": "line", "comparison_enabled": true, "default_comparison": "yesterday_vs_today", "filters": ["tenant_id", "date_range", "user_status"], "recommended_for": ["admin", "manager"]}, {"name": "Lead Generation Performance", "description": "Monitor lead creation and conversion trends across teams and time periods", "category": "sales_performance", "metrics": ["created_lead_count", "updated_lead_count", "lead_conversion_rate"], "chart_type": "bar", "comparison_enabled": true, "default_comparison": "last_month_vs_this_month", "filters": ["tenant_id", "date_range", "user_role"], "recommended_for": ["sales_manager", "admin"]}, {"name": "Deal Pipeline Analysis", "description": "Analyze deal creation, updates, and closure patterns for sales forecasting", "category": "sales_performance", "metrics": ["created_deal_count", "updated_deal_count", "deal_value_trends"], "chart_type": "combination", "comparison_enabled": true, "default_comparison": "last_quarter_vs_this_quarter", "filters": ["tenant_id", "date_range", "deal_stage"], "recommended_for": ["sales_manager", "executive"]}, {"name": "User Engagement Heatmap", "description": "Visualize user activity patterns by day of week and hour to optimize support", "category": "user_activity", "metrics": ["login_frequency", "activity_duration", "feature_usage"], "chart_type": "heatmap", "comparison_enabled": false, "filters": ["tenant_id", "date_range", "user_type"], "recommended_for": ["admin", "support_manager"]}, {"name": "Feature Adoption Dashboard", "description": "Track adoption rates of key features and marketplace apps across organizations", "category": "feature_usage", "metrics": ["marketplace_apps_usage", "custom_fields_usage", "workflow_usage"], "chart_type": "pie", "comparison_enabled": true, "default_comparison": "last_month_vs_this_month", "filters": ["tenant_id", "feature_category", "user_role"], "recommended_for": ["product_manager", "admin"]}, {"name": "Customer Success Scorecard", "description": "Comprehensive view of customer health metrics and success indicators", "category": "customer_success", "metrics": ["user_adoption_rate", "feature_utilization", "support_ticket_trends"], "chart_type": "scorecard", "comparison_enabled": true, "default_comparison": "last_quarter_vs_this_quarter", "filters": ["tenant_id", "industry", "company_size"], "recommended_for": ["customer_success_manager", "executive"]}, {"name": "Communication Activity Report", "description": "Monitor email, call, and meeting activity to assess team productivity", "category": "communication", "metrics": ["emails_sent", "calls_logged", "meetings_created"], "chart_type": "stacked_bar", "comparison_enabled": true, "default_comparison": "last_week_vs_this_week", "filters": ["tenant_id", "date_range", "communication_type"], "recommended_for": ["sales_manager", "team_lead"]}, {"name": "Onboarding Progress Tracker", "description": "Track new customer onboarding milestones and time-to-value metrics", "category": "onboarding", "metrics": ["account_setup_completion", "first_login_time", "feature_discovery_rate"], "chart_type": "funnel", "comparison_enabled": false, "filters": ["tenant_id", "onboarding_stage", "start_date"], "recommended_for": ["customer_success_manager", "onboarding_specialist"]}, {"name": "Industry Benchmark Report", "description": "Compare performance metrics against industry standards and peer organizations", "category": "benchmarking", "metrics": ["usage_vs_industry_avg", "adoption_vs_peers", "growth_rate_comparison"], "chart_type": "radar", "comparison_enabled": true, "default_comparison": "industry_benchmark", "filters": ["industry", "company_size", "plan_type"], "recommended_for": ["executive", "strategic_account_manager"]}, {"name": "Weekly Executive Summary", "description": "High-level overview of key metrics and trends for executive reporting", "category": "executive", "metrics": ["total_active_users", "revenue_impact", "customer_satisfaction"], "chart_type": "dashboard", "comparison_enabled": true, "default_comparison": "week_over_week", "filters": ["date_range", "business_unit"], "recommended_for": ["executive", "c_level"]}], "dashboards": [{"name": "Customer Success Overview", "description": "Main dashboard for customer success teams", "layout": "grid_3x3", "widgets": [{"report": "Customer Success Scorecard", "position": {"row": 1, "col": 1, "span": 2}}, {"report": "User Engagement Heatmap", "position": {"row": 1, "col": 3, "span": 1}}, {"report": "Feature Adoption Dashboard", "position": {"row": 2, "col": 1, "span": 1}}, {"report": "Onboarding Progress Tracker", "position": {"row": 2, "col": 2, "span": 2}}, {"report": "Daily Active Users Overview", "position": {"row": 3, "col": 1, "span": 3}}], "target_roles": ["customer_success_manager", "admin"]}, {"name": "Sales Performance Hub", "description": "Comprehensive sales analytics dashboard", "layout": "grid_2x4", "widgets": [{"report": "Lead Generation Performance", "position": {"row": 1, "col": 1, "span": 2}}, {"report": "Deal Pipeline Analysis", "position": {"row": 1, "col": 3, "span": 2}}, {"report": "Communication Activity Report", "position": {"row": 2, "col": 1, "span": 2}}, {"report": "Weekly Executive Summary", "position": {"row": 2, "col": 3, "span": 2}}], "target_roles": ["sales_manager", "sales_rep"]}, {"name": "Executive Command Center", "description": "High-level strategic overview for executives", "layout": "grid_2x3", "widgets": [{"report": "Weekly Executive Summary", "position": {"row": 1, "col": 1, "span": 3}}, {"report": "Industry Benchmark Report", "position": {"row": 2, "col": 1, "span": 1}}, {"report": "Customer Success Scorecard", "position": {"row": 2, "col": 2, "span": 1}}, {"report": "Feature Adoption Dashboard", "position": {"row": 2, "col": 3, "span": 1}}], "target_roles": ["executive", "c_level"]}], "generated_at": "2025-09-02T10:57:02.283Z", "version": "1.0"}