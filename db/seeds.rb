# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

puts "🌱 Kylas Customer Success Portal - Database Seeding"
puts "=" * 60

# Create default admin user if it doesn't exist
admin_user = User.find_or_create_by(email: '<EMAIL>') do |user|
  user.name = 'System Administrator'
  user.password = 'password123'
  user.password_confirmation = 'password123'
  user.role = 'ADMIN'
  user.deactivated = false
end

if admin_user.persisted?
  puts "✅ Default admin user created/verified: #{admin_user.email}"
else
  puts "❌ Failed to create admin user: #{admin_user.errors.full_messages.join(', ')}"
end

# Create default regular user if it doesn't exist
regular_user = User.find_or_create_by(email: '<EMAIL>') do |user|
  user.name = 'Regular User'
  user.password = 'password123'
  user.password_confirmation = 'password123'
  user.role = 'USER'
  user.deactivated = false
end

if regular_user.persisted?
  puts "✅ Default regular user created/verified: #{regular_user.email}"
else
  puts "❌ Failed to create regular user: #{regular_user.errors.full_messages.join(', ')}"
end

puts "\n🎯 Default users created:"
puts "   Admin: <EMAIL> / password123"
puts "   User:  <EMAIL> / password123"

puts "\n💡 To generate sample data for Usage Reports demo:"
puts "   Run: rake usage_reports:demo_data"
puts "\n📖 For detailed demo instructions, see: USAGE_REPORTS_DEMO.md"

puts "\n" + "=" * 60
puts "✅ Database seeding completed!"
