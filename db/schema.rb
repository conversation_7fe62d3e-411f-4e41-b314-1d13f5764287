# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2023_07_18_112649) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "uuid-ossp"

  create_table "account_detail_histories", force: :cascade do |t|
    t.string "email"
    t.string "company"
    t.string "mobile"
    t.string "industry"
    t.datetime "start_date"
    t.bigint "account_manager_id"
    t.bigint "support_executive_id"
    t.bigint "last_updated_by_id"
    t.bigint "tenant_id", null: false
    t.text "marketplace_apps_installed"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "profile_count"
    t.boolean "account_settings_completed"
    t.integer "active_user_count"
    t.integer "in_active_user_count"
    t.integer "team_count"
    t.string "plan_name"
    t.string "status"
    t.integer "number_of_custom_dashboards_created"
    t.integer "message_count"
    t.integer "number_of_marketplace_apps_installed"
    t.integer "dau_true"
    t.integer "dau_false"
    t.integer "logged_in_users_count"
    t.integer "active_workflow_count"
    t.integer "active_custom_fields_count"
    t.integer "in_active_custom_fields_count"
    t.integer "product_count"
    t.integer "import_count"
    t.integer "pipeline_count"
    t.integer "lead_count"
    t.integer "deal_count"
    t.integer "contact_count"
    t.integer "company_count"
    t.integer "call_count"
    t.integer "meeting_count"
    t.integer "task_count"
    t.integer "note_count"
    t.integer "created_quote_count"
    t.integer "updated_quote_count"
    t.integer "active_layout_count"
    t.integer "inactive_layout_count"
    t.integer "create_lead_layout_count"
    t.integer "edit_lead_layout_count"
    t.string "goal_trial_active"
    t.string "goal_addon"
    t.integer "number_of_goals_created"
    t.string "subscription_id"
    t.bigint "kylas_tenant_id"
    t.string "tenant_name"
    t.index ["account_manager_id"], name: "index_account_detail_histories_on_account_manager_id"
    t.index ["last_updated_by_id"], name: "index_account_detail_histories_on_last_updated_by_id"
    t.index ["support_executive_id"], name: "index_account_detail_histories_on_support_executive_id"
    t.index ["tenant_id"], name: "index_account_detail_histories_on_tenant_id"
  end

  create_table "account_details", force: :cascade do |t|
    t.string "email"
    t.string "company"
    t.string "mobile"
    t.string "industry"
    t.datetime "start_date"
    t.bigint "account_manager_id"
    t.bigint "support_executive_id"
    t.bigint "last_updated_by_id"
    t.bigint "tenant_id", null: false
    t.text "marketplace_apps_installed"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "profile_count"
    t.boolean "account_settings_completed"
    t.integer "active_user_count"
    t.integer "in_active_user_count"
    t.integer "team_count"
    t.string "plan_name"
    t.string "status"
    t.integer "number_of_custom_dashboards_created"
    t.integer "message_count"
    t.integer "number_of_marketplace_apps_installed"
    t.integer "dau_true"
    t.integer "dau_false"
    t.integer "logged_in_users_count"
    t.integer "active_workflow_count"
    t.integer "active_custom_fields_count"
    t.integer "in_active_custom_fields_count"
    t.integer "product_count"
    t.integer "import_count"
    t.integer "pipeline_count"
    t.integer "lead_count"
    t.integer "deal_count"
    t.integer "contact_count"
    t.integer "company_count"
    t.integer "call_count"
    t.integer "meeting_count"
    t.integer "task_count"
    t.integer "note_count"
    t.integer "created_quote_count"
    t.integer "updated_quote_count"
    t.integer "active_layout_count"
    t.integer "inactive_layout_count"
    t.integer "create_lead_layout_count"
    t.integer "edit_lead_layout_count"
    t.string "goal_trial_active"
    t.string "goal_addon"
    t.integer "number_of_goals_created"
    t.string "subscription_id"
    t.datetime "last_updated_at"
    t.bigint "kylas_tenant_id"
    t.string "tenant_name"
    t.index ["account_manager_id"], name: "index_account_details_on_account_manager_id"
    t.index ["last_updated_by_id"], name: "index_account_details_on_last_updated_by_id"
    t.index ["support_executive_id"], name: "index_account_details_on_support_executive_id"
    t.index ["tenant_id"], name: "index_account_details_on_tenant_id"
  end

  create_table "customer_asks", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "last_updated_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["last_updated_by_id"], name: "index_customer_asks_on_last_updated_by_id"
    t.index ["tenant_id"], name: "index_customer_asks_on_tenant_id"
  end

  create_table "customer_requirements", force: :cascade do |t|
    t.string "status"
    t.string "category"
    t.text "description"
    t.datetime "due_date"
    t.bigint "customer_ask_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_ask_id"], name: "index_customer_requirements_on_customer_ask_id"
  end

  create_table "entities", force: :cascade do |t|
    t.string "category"
    t.boolean "critical"
    t.string "frequency"
    t.integer "expected_volume"
    t.bigint "rgs_input_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["rgs_input_id"], name: "index_entities_on_rgs_input_id"
  end

  create_table "marketplace_apps", force: :cascade do |t|
    t.string "name"
    t.boolean "integrated"
    t.bigint "rgs_input_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["rgs_input_id"], name: "index_marketplace_apps_on_rgs_input_id"
  end

  create_table "rgs_inputs", force: :cascade do |t|
    t.integer "total_users"
    t.integer "total_managers"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_rgs_inputs_on_tenant_id"
  end

  create_table "tenants", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "system_updated_at"
    t.bigint "kylas_tenant_id"
  end

  create_table "usage_histories", force: :cascade do |t|
    t.bigint "user_id"
    t.string "full_name"
    t.string "email"
    t.string "plan_name"
    t.string "status"
    t.datetime "last_login_at"
    t.datetime "deactivated_at"
    t.boolean "active"
    t.boolean "verified"
    t.integer "created_lead_count"
    t.integer "created_deal_count"
    t.integer "created_contact_count"
    t.integer "updated_lead_count"
    t.integer "updated_deal_count"
    t.integer "updated_contact_count"
    t.integer "created_task_count"
    t.integer "created_note_count"
    t.integer "created_meeting_count"
    t.integer "created_company_count"
    t.integer "updated_company_count"
    t.boolean "email_account_connected"
    t.string "connected_account_name"
    t.boolean "logged_in"
    t.integer "calls_logged"
    t.integer "emails_sent"
    t.datetime "date"
    t.integer "number_of_custom_dashboards_created"
    t.integer "message_count"
    t.integer "number_of_marketplace_apps_installed"
    t.text "name_of_marketplace_apps_installed"
    t.boolean "calendar_account_connected"
    t.string "connected_calendar_account_name"
    t.integer "created_quote_count"
    t.integer "updated_quote_count"
    t.string "primary_phone_number"
    t.text "all_phone_numbers"
    t.boolean "dau"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "kylas_tenant_id"
    t.string "tenant_name"
    t.index ["tenant_id"], name: "index_usage_histories_on_tenant_id"
  end

  create_table "usages", force: :cascade do |t|
    t.bigint "user_id"
    t.string "full_name"
    t.string "email"
    t.string "plan_name"
    t.string "status"
    t.datetime "last_login_at"
    t.datetime "deactivated_at"
    t.boolean "active"
    t.boolean "verified"
    t.integer "created_lead_count"
    t.integer "created_deal_count"
    t.integer "created_contact_count"
    t.integer "updated_lead_count"
    t.integer "updated_deal_count"
    t.integer "updated_contact_count"
    t.integer "created_task_count"
    t.integer "created_note_count"
    t.integer "created_meeting_count"
    t.integer "created_company_count"
    t.integer "updated_company_count"
    t.boolean "email_account_connected"
    t.string "connected_account_name"
    t.boolean "logged_in"
    t.integer "calls_logged"
    t.integer "emails_sent"
    t.datetime "date"
    t.integer "number_of_custom_dashboards_created"
    t.integer "message_count"
    t.integer "number_of_marketplace_apps_installed"
    t.text "name_of_marketplace_apps_installed"
    t.boolean "calendar_account_connected"
    t.string "connected_calendar_account_name"
    t.integer "created_quote_count"
    t.integer "updated_quote_count"
    t.string "primary_phone_number"
    t.text "all_phone_numbers"
    t.boolean "dau"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "kylas_tenant_id"
    t.string "tenant_name"
    t.index ["tenant_id"], name: "index_usages_on_tenant_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "encrypted_password", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "name"
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.boolean "deactivated"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "account_detail_histories", "users", column: "account_manager_id"
  add_foreign_key "account_detail_histories", "users", column: "last_updated_by_id"
  add_foreign_key "account_detail_histories", "users", column: "support_executive_id"
  add_foreign_key "account_details", "users", column: "account_manager_id"
  add_foreign_key "account_details", "users", column: "last_updated_by_id"
  add_foreign_key "account_details", "users", column: "support_executive_id"
  add_foreign_key "customer_asks", "users", column: "last_updated_by_id"
end
