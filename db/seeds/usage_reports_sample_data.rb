# frozen_string_literal: true

# Sample Data for Usage Reports Demo
# This file creates realistic sample data to demonstrate the usage reporting functionality

puts "🌱 Creating sample data for Usage Reports..."

# Create sample tenants if they don't exist
sample_tenants = []

# Create 10 diverse tenants for more comprehensive data
tenant_data = [
  { name: "TechCorp Solutions", industry: "Technology", size: "large" },
  { name: "Global Innovations Inc", industry: "Manufacturing", size: "large" },
  { name: "Digital Dynamics LLC", industry: "Consulting", size: "medium" },
  { name: "StartupHub Ventures", industry: "Technology", size: "small" },
  { name: "Healthcare Plus", industry: "Healthcare", size: "medium" },
  { name: "EduTech Academy", industry: "Education", size: "medium" },
  { name: "FinanceFlow Corp", industry: "Finance", size: "large" },
  { name: "RetailMax Solutions", industry: "Retail", size: "large" },
  { name: "GreenEnergy Co", industry: "Energy", size: "medium" },
  { name: "LogisticsPro Inc", industry: "Logistics", size: "small" }
]

tenant_data.each_with_index do |tenant_info, i|
  tenant = Tenant.find_or_create_by(kylas_tenant_id: 1000 + i) do |t|
    t.name = tenant_info[:name]
    t.system_updated_at = Time.current
  end
  tenant.update(name: tenant_info[:name]) # Ensure name is updated if tenant exists
  sample_tenants << { tenant: tenant, info: tenant_info }
  puts "✅ Created tenant: #{tenant.name} (ID: #{tenant.kylas_tenant_id})"
end

# Create account details for each tenant
sample_tenants.each_with_index do |tenant_data, index|
  tenant = tenant_data[:tenant]
  info = tenant_data[:info]

  # Define user counts based on company size
  user_counts = case info[:size]
  when "small" then { active: rand(5..15), inactive: rand(1..3) }
  when "medium" then { active: rand(15..50), inactive: rand(3..8) }
  when "large" then { active: rand(50..150), inactive: rand(5..15) }
  end

  account_detail = AccountDetail.find_or_create_by(tenant: tenant) do |ad|
    domain = tenant.name.downcase.gsub(/[^a-z]/, '')
    ad.email = "contact@#{domain}.com"
    ad.company = tenant.name
    ad.industry = info[:industry]
    ad.mobile = "******-#{sprintf('%04d', 1000 + index)}"
    ad.start_date = rand(2.years.ago..6.months.ago)
    ad.kylas_tenant_id = tenant.kylas_tenant_id
    ad.tenant_name = tenant.name
    ad.profile_count = user_counts[:active] + user_counts[:inactive]
    ad.account_settings_completed = true
    ad.active_user_count = user_counts[:active]
    ad.in_active_user_count = user_counts[:inactive]

    # More diverse marketplace apps based on industry
    base_apps = ["Kylas SMS", "Email Templates"]
    industry_apps = case info[:industry]
    when "Technology" then ["Custom Fields", "Workflows", "API Integration", "Advanced Analytics"]
    when "Healthcare" then ["HIPAA Compliance", "Patient Management", "Appointment Scheduler"]
    when "Finance" then ["Financial Reports", "Compliance Tools", "Risk Management"]
    when "Education" then ["Student Portal", "Course Management", "Grade Tracker"]
    when "Retail" then ["Inventory Management", "POS Integration", "Customer Loyalty"]
    else ["Custom Fields", "Workflows", "File Storage"]
    end

    ad.marketplace_apps_installed = base_apps + industry_apps.sample(rand(1..3))
    ad.last_updated_at = Time.current
  end
  puts "✅ Created account detail for: #{tenant.name} (#{user_counts[:active]} active users)"
end

# Create comprehensive usage history data for the last 180 days (6 months)
puts "\n📊 Generating usage history data for the last 180 days..."

sample_tenants.each_with_index do |tenant_data, tenant_index|
  tenant = tenant_data[:tenant]
  info = tenant_data[:info]

  # Get user count from account detail
  account_detail = AccountDetail.find_by(tenant: tenant)
  user_count = account_detail&.active_user_count || 20

  puts "Generating data for #{tenant.name} (#{user_count} users)..."

  # Generate data for each day in the last 180 days for more comprehensive dataset
  (180.days.ago.to_date..Date.current).each do |date|
    # Create multiple user records per day to simulate real usage
    user_count.times do |user_index|
      # Simulate different user behavior patterns
      is_weekend = date.saturday? || date.sunday?
      is_holiday = [Date.new(date.year, 1, 1), Date.new(date.year, 7, 4), Date.new(date.year, 12, 25)].include?(date)
      is_active_user = user_index < (user_count * 0.85) # 85% active users
      is_power_user = user_index < (user_count * 0.25) # 25% power users
      is_manager = user_index < (user_count * 0.15) # 15% managers

      # Calculate activity multipliers based on patterns
      weekend_multiplier = is_weekend ? 0.2 : 1.0
      holiday_multiplier = is_holiday ? 0.1 : 1.0
      active_multiplier = is_active_user ? 1.0 : 0.1
      power_multiplier = is_power_user ? 2.5 : 1.0
      manager_multiplier = is_manager ? 1.8 : 1.0

      # Industry-specific patterns
      industry_multiplier = case info[:industry]
      when "Technology" then 1.3
      when "Healthcare" then 1.1
      when "Finance" then 1.2
      when "Education" then is_weekend ? 0.1 : 1.0 # Very low weekend activity
      when "Retail" then is_weekend ? 1.2 : 1.0 # Higher weekend activity
      else 1.0
      end

      # Add seasonal trends and growth
      days_ago = (Date.current - date).to_i
      growth_trend = 1.0 + (180 - days_ago) * 0.005 # Gradual growth over time
      seasonal_factor = 1.0 + 0.2 * Math.sin(2 * Math::PI * date.yday / 365.0) # Seasonal variation
      random_factor = 0.6 + rand * 0.8 # Random factor between 0.6 and 1.4

      base_multiplier = weekend_multiplier * holiday_multiplier * active_multiplier *
                       power_multiplier * manager_multiplier * industry_multiplier *
                       growth_trend * seasonal_factor * random_factor
      
      # Generate realistic names and emails
      first_names = ["John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "James", "Maria",
                     "William", "Jennifer", "Richard", "Patricia", "Charles", "Linda", "Joseph", "Elizabeth",
                     "Thomas", "Barbara", "Christopher", "Susan", "Daniel", "Jessica", "Matthew", "Karen"]
      last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez",
                    "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor",
                    "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White", "Harris"]

      first_name = first_names[user_index % first_names.length]
      last_name = last_names[(user_index + tenant_index) % last_names.length]
      full_name = "#{first_name} #{last_name}"
      domain = tenant.name.downcase.gsub(/[^a-z]/, '')
      email = "#{first_name.downcase}.#{last_name.downcase}@#{domain}.com"

      # Plan assignment based on company size and user type
      plan_name = case info[:size]
      when "small" then is_manager ? "Elevate" : "Embark"
      when "medium" then is_manager ? "Exceed" : (is_power_user ? "Elevate" : "Embark")
      when "large" then is_manager ? "Exceed" : (is_power_user ? "Elevate" : "Embark")
      end

      usage_data = {
        tenant: tenant,
        user_id: 10000 + (tenant_index * 1000) + user_index,
        full_name: full_name,
        email: email,
        plan_name: plan_name,
        status: is_active_user ? 'active' : 'inactive',
        last_login_at: is_active_user && !is_weekend && !is_holiday ? date.beginning_of_day + rand(10).hours : nil,
        deactivated_at: is_active_user ? nil : date - rand(30).days,
        active: is_active_user,
        verified: true,
        logged_in: is_active_user && !is_weekend && !is_holiday && rand < 0.85,
        dau: is_active_user && !is_weekend && !is_holiday && rand < 0.65,
        date: date,
        kylas_tenant_id: tenant.kylas_tenant_id,
        tenant_name: tenant.name,

        # Activity metrics with realistic patterns based on role and industry
        created_lead_count: (rand(0..(is_manager ? 12 : 8)) * base_multiplier).round,
        created_deal_count: (rand(0..(is_power_user ? 5 : 3)) * base_multiplier).round,
        created_contact_count: (rand(0..(is_power_user ? 8 : 5)) * base_multiplier).round,
        updated_lead_count: (rand(0..(is_active_user ? 10 : 2)) * base_multiplier).round,
        updated_deal_count: (rand(0..(is_power_user ? 6 : 4)) * base_multiplier).round,
        updated_contact_count: (rand(0..(is_active_user ? 6 : 2)) * base_multiplier).round,
        created_task_count: (rand(0..(is_manager ? 10 : 6)) * base_multiplier).round,
        created_note_count: (rand(0..(is_active_user ? 6 : 2)) * base_multiplier).round,
        created_meeting_count: (rand(0..(is_manager ? 4 : 2)) * base_multiplier).round,
        created_company_count: (rand(0..(is_power_user ? 2 : 1)) * base_multiplier).round,
        updated_company_count: (rand(0..(is_active_user ? 2 : 1)) * base_multiplier).round,
        created_quote_count: (rand(0..(is_power_user ? 3 : 1)) * base_multiplier).round,
        updated_quote_count: (rand(0..(is_active_user ? 2 : 1)) * base_multiplier).round,
        calls_logged: (rand(0..(is_manager ? 8 : 5)) * base_multiplier).round,
        emails_sent: (rand(0..(is_power_user ? 12 : 6)) * base_multiplier).round,
        message_count: (rand(0..(is_active_user ? 15 : 5)) * base_multiplier).round,
      }

      # Integration usage based on role and industry
      email_connected_prob = is_manager ? 0.9 : (is_power_user ? 0.7 : 0.4)
      calendar_connected_prob = is_manager ? 0.8 : (is_power_user ? 0.6 : 0.3)

      usage_data.merge!({
        email_account_connected: rand < email_connected_prob,
        connected_account_name: rand < email_connected_prob ? email : nil,
        calendar_account_connected: rand < calendar_connected_prob,
        connected_calendar_account_name: rand < calendar_connected_prob ? email : nil,
      })

      # Marketplace apps based on role and industry
      base_apps = ["Kylas SMS", "Email Templates"]
      industry_specific_apps = case info[:industry]
      when "Technology" then ["API Integration", "Advanced Analytics", "Custom Fields", "Workflows", "Developer Tools"]
      when "Healthcare" then ["HIPAA Compliance", "Patient Management", "Appointment Scheduler", "Medical Records"]
      when "Finance" then ["Financial Reports", "Compliance Tools", "Risk Management", "Audit Trail"]
      when "Education" then ["Student Portal", "Course Management", "Grade Tracker", "Parent Communication"]
      when "Retail" then ["Inventory Management", "POS Integration", "Customer Loyalty", "E-commerce Tools"]
      else ["Custom Fields", "Workflows", "File Storage", "Team Collaboration"]
      end

      app_count = if is_manager
        rand(3..6)
      elsif is_power_user
        rand(2..4)
      else
        rand(0..2)
      end

      installed_apps = (base_apps + industry_specific_apps.sample(app_count)).uniq

      # Custom dashboards based on role
      dashboard_count = if is_manager
        rand(2..5)
      elsif is_power_user
        rand(1..3)
      else
        rand(0..1)
      end

      usage_data.merge!({
        number_of_marketplace_apps_installed: installed_apps.length,
        name_of_marketplace_apps_installed: installed_apps,
        number_of_custom_dashboards_created: dashboard_count,

        # Phone numbers with realistic formatting
        primary_phone_number: "******-#{sprintf('%03d', tenant_index + 100)}-#{sprintf('%04d', user_index)}",
        all_phone_numbers: ["******-#{sprintf('%03d', tenant_index + 100)}-#{sprintf('%04d', user_index)}"]
      })

      # Skip creating record if it already exists to avoid duplicates
      unless UsageHistory.exists?(tenant: tenant, user_id: usage_data[:user_id], date: date)
        UsageHistory.create!(usage_data)
      end
    end

    # Progress indicator for large datasets
    if (date - 180.days.ago.to_date) % 30 == 0
      days_processed = (date - 180.days.ago.to_date).to_i + 1
      puts "  Progress: #{days_processed}/180 days processed for #{tenant.name}"
    end
  end

  total_records = UsageHistory.where(tenant: tenant).count
  puts "✅ Generated usage data for #{tenant.name} (#{user_count} users × 180 days = #{total_records} records)"
end

# Create some customer asks for demonstration
puts "\n💬 Creating sample customer asks and requirements..."

sample_tenants.each_with_index do |tenant_data, index|
  tenant = tenant_data[:tenant]
  info = tenant_data[:info]
  customer_ask = CustomerAsk.find_or_create_by(tenant: tenant)
  
  # Create sample requirements
  sample_requirements = [
    {
      category: 'LEAD',
      status: 'IN_PROGRESS',
      description: 'Need ability to import leads from LinkedIn Sales Navigator'
    },
    {
      category: 'AUTOMATION',
      status: 'COMPLETED',
      description: 'Automated email sequences for lead nurturing'
    },
    {
      category: 'INTEGRATION',
      status: 'PENDING',
      description: 'Integration with Slack for team notifications'
    },
    {
      category: 'CUSTOMISATION',
      status: 'ACCEPTED',
      description: 'Custom fields for industry-specific data capture'
    }
  ]
  
  sample_requirements.each do |req_data|
    CustomerRequirement.find_or_create_by(
      customer_ask: customer_ask,
      category: req_data[:category],
      description: req_data[:description]
    ) do |cr|
      cr.status = req_data[:status]
    end
  end
  
  puts "✅ Created customer requirements for #{tenant.name}"
end

# Create RGS inputs for demonstration
puts "\n📈 Creating sample RGS inputs..."

sample_tenants.each_with_index do |tenant_data, index|
  tenant = tenant_data[:tenant]
  info = tenant_data[:info]
  account_detail = AccountDetail.find_by(tenant: tenant)

  rgs_input = RgsInput.find_or_create_by(tenant: tenant) do |rgs|
    rgs.total_users = account_detail&.profile_count || 25
    rgs.total_managers = (account_detail&.profile_count || 25) * 0.15
  end
  
  # Create sample entities
  entity_data = [
    { category: 'LEAD', critical: true, frequency: 'DAILY', expected_volume: 50 },
    { category: 'DEAL', critical: true, frequency: 'DAILY', expected_volume: 20 },
    { category: 'CONTACT', critical: false, frequency: 'DAILY', expected_volume: 30 },
    { category: 'COMPANY', critical: false, frequency: 'DAILY', expected_volume: 10 }
  ]
  
  entity_data.each do |entity_attrs|
    Entity.find_or_create_by(
      rgs_input: rgs_input,
      category: entity_attrs[:category]
    ) do |entity|
      entity.critical = entity_attrs[:critical]
      entity.frequency = entity_attrs[:frequency]
      entity.expected_volume = entity_attrs[:expected_volume] * (index + 1)
    end
  end
  
  # Create marketplace apps based on industry
  base_apps = [
    { name: 'Kylas SMS', integrated: true },
    { name: 'Email Templates', integrated: true }
  ]

  industry_apps = case info[:industry]
  when "Technology"
    [
      { name: 'API Integration', integrated: true },
      { name: 'Advanced Analytics', integrated: true },
      { name: 'Developer Tools', integrated: rand < 0.8 },
      { name: 'Custom Fields', integrated: true }
    ]
  when "Healthcare"
    [
      { name: 'HIPAA Compliance', integrated: true },
      { name: 'Patient Management', integrated: true },
      { name: 'Appointment Scheduler', integrated: rand < 0.9 }
    ]
  when "Finance"
    [
      { name: 'Financial Reports', integrated: true },
      { name: 'Compliance Tools', integrated: true },
      { name: 'Risk Management', integrated: rand < 0.7 }
    ]
  when "Education"
    [
      { name: 'Student Portal', integrated: true },
      { name: 'Course Management', integrated: rand < 0.8 },
      { name: 'Grade Tracker', integrated: rand < 0.6 }
    ]
  when "Retail"
    [
      { name: 'Inventory Management', integrated: true },
      { name: 'POS Integration', integrated: rand < 0.9 },
      { name: 'Customer Loyalty', integrated: rand < 0.7 }
    ]
  else
    [
      { name: 'Custom Fields', integrated: true },
      { name: 'Workflows', integrated: rand < 0.8 },
      { name: 'File Storage', integrated: rand < 0.6 }
    ]
  end

  all_apps = base_apps + industry_apps

  all_apps.each do |app_attrs|
    MarketplaceApp.find_or_create_by(
      rgs_input: rgs_input,
      name: app_attrs[:name]
    ) do |app|
      app.integrated = app_attrs[:integrated]
    end
  end
  
  puts "✅ Created RGS inputs for #{tenant.name}"
end

puts "\n🎉 Sample data creation completed!"
puts "\n📋 Summary:"
puts "   • #{sample_tenants.count} sample tenants created"
puts "   • #{AccountDetail.count} account details created"
puts "   • #{UsageHistory.count} usage history records created"
puts "   • #{CustomerAsk.count} customer asks created"
puts "   • #{CustomerRequirement.count} customer requirements created"
puts "   • #{RgsInput.count} RGS inputs created"
puts "   • #{Entity.count} entities created"
puts "   • #{MarketplaceApp.count} marketplace apps created"

puts "\n🚀 You can now:"
puts "   1. Navigate to any account detail page"
puts "   2. Click on the 'Usage Reports' tab"
puts "   3. Try different report types and comparison options"
puts "   4. Export reports as CSV files"
puts "\n💡 Recommended reports to try:"
puts "   • Daily Logged In Users with Yesterday vs Today comparison"
puts "   • Daily Lead Creation with Last Month vs This Month comparison"
puts "   • User Activity Summary with Last Quarter vs This Quarter comparison"
puts "   • Feature Usage Overview with no comparison"
