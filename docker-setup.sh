#!/bin/bash

# Docker Setup Script for Kylas Customer Success Portal
# This script sets up the application using Docker Compose

set -e

echo "🐳 Docker Setup for Kylas Customer Success Portal"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed"
}

# Start PostgreSQL containers
start_databases() {
    echo "🗄️  Starting PostgreSQL containers..."
    docker-compose up -d postgres postgres_test
    
    echo "⏳ Waiting for PostgreSQL to be ready..."
    sleep 10
    
    # Wait for PostgreSQL to be healthy
    echo "🔍 Checking PostgreSQL health..."
    docker-compose exec postgres pg_isready -U postgres || {
        print_warning "PostgreSQL is not ready yet, waiting a bit more..."
        sleep 10
        docker-compose exec postgres pg_isready -U postgres
    }
    
    print_status "PostgreSQL containers are running and healthy"
}

# Setup Rails application
setup_rails() {
    echo "🚀 Setting up Rails application..."
    
    # Build the Rails container
    echo "🔨 Building Rails container..."
    docker-compose build web
    
    # Install dependencies
    echo "📦 Installing Ruby dependencies..."
    docker-compose run --rm web bundle install
    
    # Setup databases
    echo "🗄️  Setting up databases..."
    docker-compose run --rm web rails db:create
    docker-compose run --rm web rails db:migrate
    docker-compose run --rm web rails db:seed
    
    print_status "Rails application setup completed"
}

# Generate sample data
generate_sample_data() {
    echo "📊 Generating sample data..."
    
    # Generate comprehensive sample data
    docker-compose run --rm web rake usage_reports:demo_data
    
    # Generate default reports
    docker-compose run --rm web rails runner "load Rails.root.join('db', 'seeds', 'default_reports.rb')"
    
    print_status "Sample data generation completed"
}

# Start the application
start_application() {
    echo "🌐 Starting the application..."
    docker-compose up -d web
    
    print_status "Application is starting..."
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "📋 Application Information:"
    echo "   🌐 Web Application: http://localhost:3000"
    echo "   🗄️  PostgreSQL (Dev): localhost:5432"
    echo "   🗄️  PostgreSQL (Test): localhost:5433"
    echo ""
    echo "🔑 Default Login Credentials:"
    echo "   👤 Admin: <EMAIL> / password123"
    echo "   👤 User:  <EMAIL> / password123"
    echo ""
    echo "🛠️  Useful Commands:"
    echo "   📊 View logs: docker-compose logs -f web"
    echo "   🔄 Restart: docker-compose restart web"
    echo "   🛑 Stop: docker-compose down"
    echo "   🗄️  Database console: docker-compose exec postgres psql -U postgres -d kylas_customer_success_development"
    echo ""
}

# Main execution
main() {
    check_docker
    start_databases
    setup_rails
    generate_sample_data
    start_application
}

# Handle script arguments
case "${1:-setup}" in
    "setup")
        main
        ;;
    "start")
        echo "🚀 Starting existing containers..."
        docker-compose up -d
        print_status "Containers started"
        ;;
    "stop")
        echo "🛑 Stopping containers..."
        docker-compose down
        print_status "Containers stopped"
        ;;
    "restart")
        echo "🔄 Restarting containers..."
        docker-compose restart
        print_status "Containers restarted"
        ;;
    "logs")
        echo "📊 Showing application logs..."
        docker-compose logs -f web
        ;;
    "console")
        echo "🗄️  Opening database console..."
        docker-compose exec postgres psql -U postgres -d kylas_customer_success_development
        ;;
    "rails")
        echo "🚂 Opening Rails console..."
        docker-compose exec web rails console
        ;;
    "clean")
        echo "🧹 Cleaning up containers and volumes..."
        docker-compose down -v
        docker system prune -f
        print_status "Cleanup completed"
        ;;
    *)
        echo "Usage: $0 {setup|start|stop|restart|logs|console|rails|clean}"
        echo ""
        echo "Commands:"
        echo "  setup   - Full setup (default)"
        echo "  start   - Start existing containers"
        echo "  stop    - Stop containers"
        echo "  restart - Restart containers"
        echo "  logs    - Show application logs"
        echo "  console - Open database console"
        echo "  rails   - Open Rails console"
        echo "  clean   - Clean up containers and volumes"
        exit 1
        ;;
esac
