#!/bin/bash

# Usage Reports Demo Setup Script
# This script sets up the complete demo environment for the Usage Reports feature

echo "🚀 Kylas Customer Success - Usage Reports Demo Setup"
echo "=" * 60

# Check if Rails is available
if ! command -v rails &> /dev/null; then
    echo "❌ Rails is not installed or not in PATH"
    echo "Please install Rails and try again"
    exit 1
fi

# Check if we're in a Rails project
if [ ! -f "Gemfile" ] || [ ! -f "config/application.rb" ]; then
    echo "❌ This doesn't appear to be a Rails project directory"
    echo "Please run this script from the Rails project root"
    exit 1
fi

echo "✅ Rails environment detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
bundle install

# Setup database
echo ""
echo "🗄️  Setting up database..."
rails db:create
rails db:migrate

# Run basic seeds
echo ""
echo "🌱 Running basic database seeds..."
rails db:seed

# Generate usage reports demo data
echo ""
echo "📊 Generating Usage Reports demo data..."
echo "This may take a few minutes as we're creating 90 days of realistic usage data..."
rails usage_reports:demo_data

# Check if everything was created successfully
echo ""
echo "🔍 Verifying demo data..."
rails usage_reports:stats

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "🚀 Next Steps:"
echo "1. Start the Rails server:"
echo "   rails server"
echo ""
echo "2. Open your browser and navigate to:"
echo "   http://localhost:3000"
echo ""
echo "3. Login with one of these accounts:"
echo "   Admin: <EMAIL> / password123"
echo "   User:  <EMAIL> / password123"
echo ""
echo "4. Navigate to any account detail page and click 'Usage Reports' tab"
echo ""
echo "📖 For detailed testing instructions, see: USAGE_REPORTS_DEMO.md"
echo ""
echo "🎯 Sample Accounts Available:"
echo "   • TechCorp Solutions (Technology, 15 users)"
echo "   • Global Innovations Inc (Manufacturing, 25 users)"
echo "   • Digital Dynamics LLC (Consulting, 40 users)"
echo ""
echo "💡 Recommended First Test:"
echo "   Report Type: Daily Logged In Users"
echo "   Comparison: Yesterday vs Today"
echo "   Date Range: Last 7 Days"
echo ""
echo "Happy testing! 🎊"
