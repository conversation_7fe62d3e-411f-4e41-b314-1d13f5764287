#!/usr/bin/env ruby

# SQLite to PostgreSQL Migration Script for Kylas Customer Success Portal
# This script helps migrate existing SQLite data to PostgreSQL

require 'sqlite3'
require 'pg'
require 'json'
require 'yaml'

class SqliteToPostgresqlMigrator
  def initialize
    @sqlite_db_path = 'db/development.sqlite3'
    @config = load_database_config
    @pg_config = @config['development']
    @tables_to_migrate = %w[
      users tenants account_details account_detail_histories
      usage_histories usages customer_asks customer_requirements
      rgs_inputs entities marketplace_apps
    ]
  end

  def migrate!
    puts "🔄 Starting SQLite to PostgreSQL Migration"
    puts "=" * 60

    # Check if SQLite database exists
    unless File.exist?(@sqlite_db_path)
      puts "❌ SQLite database not found at #{@sqlite_db_path}"
      puts "💡 This is normal for new installations. Skipping migration."
      return
    end

    # Connect to databases
    connect_to_databases

    # Check if PostgreSQL is empty
    if postgresql_has_data?
      puts "⚠️  PostgreSQL database already contains data."
      print "Do you want to continue and potentially overwrite data? (y/N): "
      response = STDIN.gets.chomp.downcase
      return unless response == 'y' || response == 'yes'
    end

    # Perform migration
    migrate_tables
    update_sequences
    verify_migration

    puts "\n🎉 Migration completed successfully!"
    puts "💡 You can now remove the SQLite database files if desired."

  rescue => e
    puts "❌ Migration failed: #{e.message}"
    puts e.backtrace.first(5).join("\n")
    exit 1
  ensure
    close_connections
  end

  private

  def load_database_config
    config_path = 'config/database.yml'
    unless File.exist?(config_path)
      puts "❌ Database configuration not found at #{config_path}"
      exit 1
    end
    YAML.load_file(config_path)
  end

  def connect_to_databases
    puts "🔌 Connecting to databases..."
    
    @sqlite_db = SQLite3::Database.new(@sqlite_db_path)
    @sqlite_db.results_as_hash = true
    
    @pg_db = PG.connect(
      host: @pg_config['host'],
      port: @pg_config['port'],
      dbname: @pg_config['database'],
      user: @pg_config['username'],
      password: @pg_config['password']
    )
    
    puts "✅ Connected to both databases"
  end

  def postgresql_has_data?
    result = @pg_db.exec("SELECT COUNT(*) as count FROM users")
    result[0]['count'].to_i > 0
  end

  def migrate_tables
    @tables_to_migrate.each do |table|
      migrate_table(table)
    end
  end

  def migrate_table(table_name)
    puts "\n📋 Migrating table: #{table_name}"
    
    # Check if table exists in SQLite
    sqlite_tables = @sqlite_db.execute("SELECT name FROM sqlite_master WHERE type='table'")
    unless sqlite_tables.any? { |row| row['name'] == table_name }
      puts "⚠️  Table #{table_name} not found in SQLite, skipping..."
      return
    end

    # Get data from SQLite
    rows = @sqlite_db.execute("SELECT * FROM #{table_name}")
    
    if rows.empty?
      puts "   📭 No data found in #{table_name}"
      return
    end

    puts "   📊 Found #{rows.length} records"

    # Clear existing data in PostgreSQL
    @pg_db.exec("TRUNCATE TABLE #{table_name} RESTART IDENTITY CASCADE")

    # Get column names
    columns = rows.first.keys
    
    # Prepare insert statement
    placeholders = (1..columns.length).map { |i| "$#{i}" }.join(', ')
    insert_sql = "INSERT INTO #{table_name} (#{columns.join(', ')}) VALUES (#{placeholders})"
    
    # Insert data
    rows.each_with_index do |row, index|
      values = columns.map { |col| convert_value(row[col]) }
      
      begin
        @pg_db.exec_params(insert_sql, values)
      rescue PG::Error => e
        puts "   ❌ Error inserting row #{index + 1}: #{e.message}"
        puts "   📝 Row data: #{row.inspect}"
        next
      end
      
      # Progress indicator
      if (index + 1) % 100 == 0
        puts "   📈 Progress: #{index + 1}/#{rows.length} records migrated"
      end
    end
    
    puts "   ✅ Migrated #{rows.length} records to #{table_name}"
  end

  def convert_value(value)
    case value
    when nil
      nil
    when String
      # Handle JSON strings
      if value.start_with?('[') || value.start_with?('{')
        begin
          JSON.parse(value)
          value
        rescue JSON::ParserError
          value
        end
      else
        value
      end
    when Integer, Float, TrueClass, FalseClass
      value
    else
      value.to_s
    end
  end

  def update_sequences
    puts "\n🔢 Updating PostgreSQL sequences..."
    
    @tables_to_migrate.each do |table|
      begin
        # Get the maximum ID from the table
        result = @pg_db.exec("SELECT MAX(id) as max_id FROM #{table}")
        max_id = result[0]['max_id']
        
        next unless max_id
        
        # Update the sequence
        sequence_name = "#{table}_id_seq"
        @pg_db.exec("SELECT setval('#{sequence_name}', #{max_id})")
        puts "   ✅ Updated sequence for #{table} to #{max_id}"
        
      rescue PG::Error => e
        puts "   ⚠️  Could not update sequence for #{table}: #{e.message}"
      end
    end
  end

  def verify_migration
    puts "\n🔍 Verifying migration..."
    
    @tables_to_migrate.each do |table|
      begin
        sqlite_count = @sqlite_db.execute("SELECT COUNT(*) as count FROM #{table}")[0]['count']
        pg_result = @pg_db.exec("SELECT COUNT(*) as count FROM #{table}")
        pg_count = pg_result[0]['count'].to_i
        
        if sqlite_count == pg_count
          puts "   ✅ #{table}: #{pg_count} records (matches SQLite)"
        else
          puts "   ⚠️  #{table}: PostgreSQL has #{pg_count} records, SQLite had #{sqlite_count}"
        end
      rescue => e
        puts "   ❌ Error verifying #{table}: #{e.message}"
      end
    end
  end

  def close_connections
    @sqlite_db&.close
    @pg_db&.close
  end
end

# Main execution
if __FILE__ == $0
  puts "🐘 SQLite to PostgreSQL Migration Tool"
  puts "======================================"
  
  migrator = SqliteToPostgresqlMigrator.new
  migrator.migrate!
end
