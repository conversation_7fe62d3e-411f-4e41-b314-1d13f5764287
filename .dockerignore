# Git
.git
.gitignore

# Documentation
README.md
POSTGRESQL_MIGRATION_SUMMARY.md
USAGE_REPORTS_DEMO.md

# Logs
log/*
tmp/*
*.log

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Database files
db/*.sqlite3
db/*.sqlite3-journal

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Rails specific
/config/master.key
/config/credentials.yml.enc
/public/assets
/public/packs
/public/packs-test
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/.keep

# Ignore uploaded files in development
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/.keep

# Ignore master key for decrypting credentials and more.
/config/master.key
