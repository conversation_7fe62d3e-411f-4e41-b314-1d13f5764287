# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "AccountDetails", type: :request do
  let(:account) { create(:account_detail) }
  let(:user) { create(:user) }
  let(:account_manager) { create(:user) }
  let(:support_executive) { create(:user) }
  let(:params) { { id: account.id, support_executive_id: support_executive.id, account_manager_id: account_manager.id } }
  let(:invalid_params) { { id: account.id, support_executive_id: 0, account_manager_id: 0 } }
  before(:each) do
    sign_in_resource(create(:user))
  end
  describe '#index' do
    let(:account_with_req_id) { create(:account_detail) }
    let(:account_matching_key) { create(:account_detail) }
    let(:deactivated_user) { create(:deactivated_user) }
    context 'if key is blank' do
      before do
        create_list(:account_detail, 10)
      end
      it 'should list all account details' do
        get '/account-details?items=10&page=1'
        assert_select 'tr', 11
        expect(response.status).to eq(200)
      end
    end

    context 'if key is integer' do
      before do
        @count = AccountDetail.joins(:tenant).where('tenant_id::text LIKE ?', "%#{account_with_req_id.tenant_id}%").limit(10).count
      end
      it 'should list all account details matching the tenant_id' do
        get "/account-details?items=10&page=1&key=#{account_with_req_id.tenant_id}"
        assert_select 'tr', @count + 1
        expect(response.status).to eq(200)
      end
    end

    context 'if key is passed' do
      it 'should list all account details matching the tenant_id' do
        get "/account-details?items=10&page=1&key=#{account_matching_key.tenant.name}"
        assert_select 'tr', 2
        expect(response.status).to eq(200)
      end
    end

    context 'When User is deactivated' do
      before do
        sign_in_resource(deactivated_user)
      end
      it 'should redirect user to sign in page and flash error message' do
        get "/account-details?items=10&page=1"
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to new_user_session_path
      end
    end
  end

  describe '#show' do
    context 'when account with id exists' do
      it 'should show selected account details' do
        get "/account-details/#{account.id}"
        assert_select 'form', 1
        assert_select 'select', 2
        expect(response.status).to eq(200)
      end
    end

    context 'when account with id does not exist' do
      it 'should show selected account details' do
        get '/account-details/111111'
        assert_redirected_to account_details_path
        expect(flash[:danger]).to eq('Account does not exist')
        expect(response.status).to eq(302)
      end
    end
  end
  describe '#update' do
    context 'when account with id exists' do
      it 'should list account details' do
        patch "/account-details/#{account.id}", params: params
        expect(flash[:success]).to eq('Account Details Updated Successfully')
        expect(response.status).to eq(302)
      end
    end

    context 'when account with id does not exist' do
      it 'should list account details' do
        patch "/account-details/111111", params: params
        assert_redirected_to account_details_path
        expect(flash[:danger]).to eq('Account does not exist')
        expect(response.status).to eq(302)
      end
    end

    context 'when account is not updated' do
      it 'should list account details' do
        patch "/account-details/#{account.id}", params: invalid_params
        assert_redirected_to account_detail_path(account)
        expect(flash[:danger]).to eq('Account details not updated due to: User does not exist')
      end
    end
  end
end
