# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UsageReportService, type: :service do
  let(:tenant) { create(:tenant) }
  let!(:usage_history_today) do
    create(:usage_history,
           tenant: tenant,
           date: Date.current - 1.day, # Use yesterday since sample data goes up to yesterday
           logged_in: true,
           created_lead_count: 10,
           created_deal_count: 5,
           active: true,
           dau: true)
  end
  let!(:usage_history_yesterday) do
    create(:usage_history,
           tenant: tenant,
           date: Date.current - 2.days,
           logged_in: true,
           created_lead_count: 8,
           created_deal_count: 3,
           active: true,
           dau: true)
  end

  describe '#generate_report' do
    context 'with single period report' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'daily_logged_in_users',
          comparison_type: 'none',
          date_range: '7_days'
        )
      end

      it 'generates daily logged in users report' do
        result = service.generate_report

        expect(result[:report_type]).to eq('daily_logged_in_users')
        expect(result[:comparison_type]).to eq('none')
        expect(result[:data]).to be_a(Hash)
        expect(result[:data]).to have_key((Date.current - 1.day).strftime('%Y-%m-%d'))
      end

      it 'includes period information' do
        result = service.generate_report

        expect(result[:period][:start]).to be_a(Date)
        expect(result[:period][:end]).to be_a(Date)
        expect(result[:generated_at]).to be_present
      end
    end

    context 'with daily lead creation report' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'daily_lead_creation',
          comparison_type: 'none',
          date_range: '7_days'
        )
      end

      it 'generates correct lead creation data' do
        result = service.generate_report

        expect(result[:data][(Date.current - 1.day).strftime('%Y-%m-%d')]).to eq(10)
        expect(result[:data][(Date.current - 2.days).strftime('%Y-%m-%d')]).to eq(8)
      end
    end

    context 'with user activity summary report' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'user_activity_summary',
          comparison_type: 'none',
          date_range: '7_days'
        )
      end

      it 'generates summary statistics' do
        result = service.generate_report

        expect(result[:data][:total_users]).to eq(2) # We have 2 usage history records
        expect(result[:data][:active_users]).to eq(2)
        expect(result[:data][:daily_active_users]).to eq(2)
      end
    end

    context 'with comparison report' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'daily_logged_in_users',
          comparison_type: 'yesterday_today',
          date_range: '7_days'
        )
      end

      it 'generates comparison data' do
        result = service.generate_report

        expect(result[:comparison_type]).to eq('yesterday_today')
        expect(result[:period1]).to be_present
        expect(result[:period2]).to be_present
        expect(result[:comparison]).to be_present
      end

      it 'calculates comparison metrics correctly' do
        result = service.generate_report

        expect(result[:comparison][:period1_total]).to eq(1) # yesterday
        expect(result[:comparison][:period2_total]).to eq(1) # today
        expect(result[:comparison][:change]).to eq(0)
        expect(result[:comparison][:percentage_change]).to eq(0.0)
      end
    end

    context 'with custom date range' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'daily_logged_in_users',
          comparison_type: 'custom',
          start_date: Date.current - 2.days,
          end_date: Date.current
        )
      end

      it 'uses custom date range' do
        result = service.generate_report

        expect(result[:period][:start]).to eq(Date.current - 2.days)
        expect(result[:period][:end]).to eq(Date.current)
      end
    end

    context 'with invalid parameters' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'invalid_report_type',
          comparison_type: 'none',
          date_range: '7_days'
        )
      end

      it 'returns error for invalid report type' do
        result = service.generate_report

        expect(result[:data][:error]).to eq('Invalid report type')
      end
    end

    context 'with custom comparison without dates' do
      let(:service) do
        UsageReportService.new(
          tenant: tenant,
          report_type: 'daily_logged_in_users',
          comparison_type: 'custom'
        )
      end

      it 'returns error when custom dates are missing' do
        result = service.generate_report

        expect(result[:error]).to eq('Custom date range requires start_date and end_date')
      end
    end
  end

  describe 'different report types' do
    let(:service_base_params) do
      {
        tenant: tenant,
        comparison_type: 'none',
        date_range: '7_days'
      }
    end

    it 'generates daily deal creation report' do
      service = UsageReportService.new(**service_base_params, report_type: 'daily_deal_creation')
      result = service.generate_report

      expect(result[:data][(Date.current - 1.day).strftime('%Y-%m-%d')]).to eq(5)
    end

    it 'generates feature usage overview report' do
      service = UsageReportService.new(**service_base_params, report_type: 'feature_usage_overview')
      result = service.generate_report

      expect(result[:data][:total_leads_created]).to eq(18) # 10 + 8
      expect(result[:data][:total_deals_created]).to eq(8)  # 5 + 3
    end

    it 'generates active inactive users report' do
      service = UsageReportService.new(**service_base_params, report_type: 'active_inactive_users')
      result = service.generate_report

      expect(result[:data][:active_users_by_date]).to be_a(Hash)
      expect(result[:data][:dau_by_date]).to be_a(Hash)
    end
  end

  describe 'date range handling' do
    it 'handles 7_days range' do
      service = UsageReportService.new(tenant: tenant, report_type: 'daily_logged_in_users', date_range: '7_days')
      start_date, end_date = service.send(:determine_date_range)

      expect(end_date).to eq(Date.current)
      expect(start_date).to eq(7.days.ago.to_date)
    end

    it 'handles 30_days range' do
      service = UsageReportService.new(tenant: tenant, report_type: 'daily_logged_in_users', date_range: '30_days')
      start_date, end_date = service.send(:determine_date_range)

      expect(end_date).to eq(Date.current)
      expect(start_date).to eq(30.days.ago.to_date)
    end

    it 'handles 1_year range' do
      service = UsageReportService.new(tenant: tenant, report_type: 'daily_logged_in_users', date_range: '1_year')
      start_date, end_date = service.send(:determine_date_range)

      expect(end_date).to eq(Date.current)
      expect(start_date).to eq(1.year.ago.to_date)
    end
  end
end
