# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ReadTenantData, type: :model do
  before(:all) do
    Tenant.delete_all
  end

  context 'when data is not populated' do
    it 'should populate the tenant data into the database' do
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: S3_ONBOARDING_SHEET_KEY,
          response_target: 'tmp/onboarding.json'
        )
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: S3_USAGE_SHEET_KEY,
          response_target: 'tmp/usage.json'
        )
      File.open('tmp/onboarding.json', 'w') do |file|
        file.write File.read('tmp/onboarding-test.json')
      end
      File.open('tmp/usage.json', 'w') do |file|
        file.write File.read('tmp/usage-test.json')
      end
      ReadTenantData.call
      expect(AccountDetail.count).to be > 0
    end
  end

  context 'when data is already populated' do
    before do
      @count = AccountDetailHistory.count
    end
    it 'should update the tenant data into the database' do
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: S3_ONBOARDING_SHEET_KEY,
          response_target: 'tmp/onboarding.json'
        )
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: S3_USAGE_SHEET_KEY,
          response_target: 'tmp/usage.json'
        )
      File.open('tmp/onboarding.json', 'w') do |file|
        file.write File.read('tmp/onboarding-test.json')
      end
      ReadTenantData.call
      expect(AccountDetailHistory.count).to be > @count
    end
  end
end
