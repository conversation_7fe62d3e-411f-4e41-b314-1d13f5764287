# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UsageReportExportService, type: :service do
  describe '#to_csv' do
    context 'with single period daily data' do
      let(:report_data) do
        {
          report_type: 'daily_logged_in_users',
          comparison_type: 'none',
          period: { start: Date.current - 7.days, end: Date.current },
          data: {
            '2024-01-01' => 10,
            '2024-01-02' => 15,
            '2024-01-03' => 8
          },
          generated_at: Time.current
        }
      end

      let(:service) { UsageReportExportService.new(report_data, 'daily_logged_in_users') }

      it 'generates CSV with correct headers' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Usage Report')
        expect(lines[1]).to include('daily_logged_in_users')
        expect(lines[5]).to eq('Date,Logged In Users')
      end

      it 'includes data rows' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        expect(lines[6]).to eq('2024-01-01,10')
        expect(lines[7]).to eq('2024-01-02,15')
        expect(lines[8]).to eq('2024-01-03,8')
      end

      it 'includes summary statistics' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        summary_start = lines.index('Summary')
        expect(summary_start).to be_present
        expect(lines[summary_start + 1]).to eq('Total Days,3')
        expect(lines[summary_start + 2]).to eq('Total,33')
        expect(lines[summary_start + 3]).to eq('Average,11.0')
        expect(lines[summary_start + 4]).to eq('Maximum,15')
        expect(lines[summary_start + 5]).to eq('Minimum,8')
      end
    end

    context 'with single period summary data' do
      let(:report_data) do
        {
          report_type: 'user_activity_summary',
          comparison_type: 'none',
          period: { start: Date.current - 7.days, end: Date.current },
          data: {
            total_users: 100,
            active_users: 85,
            daily_active_users: 45
          },
          generated_at: Time.current
        }
      end

      let(:service) { UsageReportExportService.new(report_data, 'user_activity_summary') }

      it 'generates CSV with metric-value format' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        expect(lines[5]).to eq('Metric,Value')
        expect(lines[6]).to eq('Total users,100')
        expect(lines[7]).to eq('Active users,85')
        expect(lines[8]).to eq('Daily active users,45')
      end
    end

    context 'with comparison daily data' do
      let(:report_data) do
        {
          report_type: 'daily_logged_in_users',
          comparison_type: 'yesterday_today',
          period1: {
            start: Date.current - 1.day,
            end: Date.current - 1.day,
            data: { '2024-01-01' => 10, '2024-01-02' => 12 }
          },
          period2: {
            start: Date.current,
            end: Date.current,
            data: { '2024-01-01' => 15, '2024-01-02' => 18 }
          },
          comparison: {
            period1_total: 22,
            period2_total: 33,
            change: 11,
            percentage_change: 50.0
          },
          generated_at: Time.current
        }
      end

      let(:service) { UsageReportExportService.new(report_data, 'daily_logged_in_users') }

      it 'generates comparison CSV with correct headers' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        expect(lines[0]).to eq('Usage Report Comparison')
        expect(lines[1]).to include('daily_logged_in_users')
        expect(lines[2]).to include('yesterday_today')
      end

      it 'includes comparison data' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        header_line = lines.find { |line| line.include?('Date,Period 1 Logged In Users') }
        expect(header_line).to be_present
      end

      it 'includes summary comparison' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        summary_start = lines.index('Summary Comparison')
        expect(summary_start).to be_present
        expect(lines[summary_start + 2]).to include('Total,22,33,11,50.0%')
      end
    end

    context 'with comparison summary data' do
      let(:report_data) do
        {
          report_type: 'user_activity_summary',
          comparison_type: 'last_month_this_month',
          period1: {
            start: 1.month.ago.beginning_of_month,
            end: 1.month.ago.end_of_month,
            data: { total_users: 80, active_users: 60 }
          },
          period2: {
            start: Date.current.beginning_of_month,
            end: Date.current.end_of_month,
            data: { total_users: 100, active_users: 85 }
          },
          comparison: {
            total_users: { period1: 80, period2: 100, change: 20, percentage_change: 25.0 },
            active_users: { period1: 60, period2: 85, change: 25, percentage_change: 41.67 }
          },
          generated_at: Time.current
        }
      end

      let(:service) { UsageReportExportService.new(report_data, 'user_activity_summary') }

      it 'generates summary comparison CSV' do
        csv_content = service.to_csv
        lines = csv_content.split("\n")

        header_line = lines.find { |line| line.include?('Metric,Period 1,Period 2,Change,% Change') }
        expect(header_line).to be_present

        total_users_line = lines.find { |line| line.include?('Total users,80,100,20,25.0%') }
        expect(total_users_line).to be_present

        active_users_line = lines.find { |line| line.include?('Active users,60,85,25,41.67%') }
        expect(active_users_line).to be_present
      end
    end
  end

  describe '#metric_name' do
    let(:service) { UsageReportExportService.new({}, 'daily_logged_in_users') }

    it 'returns correct metric names for different report types' do
      expect(UsageReportExportService.new({}, 'daily_logged_in_users').send(:metric_name)).to eq('Logged In Users')
      expect(UsageReportExportService.new({}, 'daily_lead_creation').send(:metric_name)).to eq('Leads Created')
      expect(UsageReportExportService.new({}, 'daily_deal_creation').send(:metric_name)).to eq('Deals Created')
      expect(UsageReportExportService.new({}, 'daily_calls_logged').send(:metric_name)).to eq('Calls Logged')
      expect(UsageReportExportService.new({}, 'unknown_type').send(:metric_name)).to eq('Value')
    end
  end

  describe 'CSV format validation' do
    let(:report_data) do
      {
        report_type: 'daily_logged_in_users',
        comparison_type: 'none',
        period: { start: Date.current - 2.days, end: Date.current },
        data: { '2024-01-01' => 10 },
        generated_at: Time.current
      }
    end

    let(:service) { UsageReportExportService.new(report_data, 'daily_logged_in_users') }

    it 'generates valid CSV format' do
      csv_content = service.to_csv
      
      expect { CSV.parse(csv_content) }.not_to raise_error
      
      parsed_csv = CSV.parse(csv_content)
      expect(parsed_csv).to be_an(Array)
      expect(parsed_csv.length).to be > 5
    end
  end
end
