# frozen_string_literal: true

class ReadTenantData < ApplicationService
  require 'aws-sdk-s3'

  def initialize
    super
    Aws.config.update({
      endpoint: AWS_END_POINT,
      region: AWS_REGION,
      credentials: Aws::Credentials.new(AWS_ACCESS_KEY, AWS_SECRET_KEY)
    })
  end

  def call
    s3_client = Aws::S3::Client.new
    begin
      s3_client.get_object(
        bucket: S3_BUCKET_NAME,
        key: S3_ONBOARDING_SHEET_KEY,
        response_target: ONBOARDING_SYSTEM_UPDATE_FILE_PATH
      )
      s3_client.get_object(
        bucket: S3_BUCKET_NAME,
        key: S3_USAGE_SHEET_KEY,
        response_target: USAGE_SYSTEM_UPDATE_FILE_PATH
      )
      onboarding_json_data = File.read(ONBOARDING_SYSTEM_UPDATE_FILE_PATH)
      usage_json_data = File.read(USAGE_SYSTEM_UPDATE_FILE_PATH)
      onboarding_data = JSON.parse(onboarding_json_data)
      usage_data = JSON.parse(usage_json_data)
      onboarding_data.each do |row|
        next if row['planName'] == EMBARK

        row.transform_keys! do |key|
          key.underscore
        end
        tenant = read_tenant_detail(row)
        read_account_detail(row, tenant)
      end
      usage_data.each do |row|
        next if row['planName'] == EMBARK

        row.transform_keys! do |key|
          key.underscore
        end
        read_usage(row)
      end
    rescue StandardError => e
      Rails.logger.error e.message
    end
  end

  def read_tenant_detail(row)
    tenant = Tenant.find_or_initialize_by(kylas_tenant_id: row['tenant_id'])
    if tenant.persisted?
      tenant.update!(name: row['tenant_name'], system_updated_at: Time.now)
    else
      tenant.save!(name: row['tenant_name'], kylas_tenant_id: row['tenant_id'], system_updated_at: Time.now)
    end
    tenant
  end

  def read_account_detail(row, tenant)
    return unless tenant.present?

    row['email'] = row.delete 'tenant_user_email'
    row['industry'] = row.delete 'tenant_industry'
    row['start_date'] = row.delete 'signed_up_at'
    row['marketplace_apps_installed'] = row['name_of_marketplace_apps_installed'].nil? ? [] : row['name_of_marketplace_apps_installed'].split(',')
    row['tenant_name'] = row.delete('tenant_name')
    row['kylas_tenant_id'] = row.delete('tenant_id')
    row['tenant_id'] = tenant.id
    row.delete('name_of_marketplace_apps_installed')
    row.delete('customer_id')
    account_detail = tenant.account_detail
    if account_detail
      account_detail.update!(**row)
    else
      AccountDetail.create!(**row)
    end
    AccountDetailHistory.create!(**row)
  end

  def read_usage(row)
    tenant = Tenant.find_by(kylas_tenant_id: row['tenant_id'])
    return unless tenant.present?

    row['name_of_marketplace_apps_installed'] = row['name_of_marketplace_apps_installed'].nil? ? [] : row['name_of_marketplace_apps_installed'].split(',')
    row['all_phone_numbers'] = row['all_phone_numbers'].nil? ? [] : row['all_phone_numbers'].split(',')
    row['tenant_name'] = row.delete 'tenant_name'
    row['kylas_tenant_id'] = row['tenant_id']
    row['tenant_id'] = tenant.id

    usage = Usage.find_by(user_id: row['user_id'])
    if usage
      usage.update!(**row)
    else
      Usage.create!(**row)
    end
    UsageHistory.create!(**row)
  end
end
