# frozen_string_literal: true

require 'csv'

class UsageReportExportService < ApplicationService
  attr_reader :report_data, :report_type

  def initialize(report_data, report_type)
    @report_data = report_data
    @report_type = report_type
  end

  def call
    to_csv
  end

  def to_csv
    CSV.generate(headers: true) do |csv|
      if comparison_report?
        generate_comparison_csv(csv)
      else
        generate_single_period_csv(csv)
      end
    end
  end

  private

  def comparison_report?
    report_data.key?(:period1) && report_data.key?(:period2)
  end

  def generate_comparison_csv(csv)
    # Add header with report information
    csv << ['Usage Report Comparison']
    csv << ['Report Type', report_data[:report_type]]
    csv << ['Comparison Type', report_data[:comparison_type]]
    csv << ['Period 1', "#{report_data[:period1][:start]} to #{report_data[:period1][:end]}"]
    csv << ['Period 2', "#{report_data[:period2][:start]} to #{report_data[:period2][:end]}"]
    csv << ['Generated At', report_data[:generated_at]]
    csv << [] # Empty row

    period1_data = report_data[:period1][:data]
    period2_data = report_data[:period2][:data]
    comparison_data = report_data[:comparison]

    if daily_data?(period1_data)
      generate_daily_comparison_csv(csv, period1_data, period2_data, comparison_data)
    else
      generate_summary_comparison_csv(csv, period1_data, period2_data, comparison_data)
    end
  end

  def generate_single_period_csv(csv)
    # Add header with report information
    csv << ['Usage Report']
    csv << ['Report Type', report_data[:report_type]]
    csv << ['Period', "#{report_data[:period][:start]} to #{report_data[:period][:end]}"]
    csv << ['Generated At', report_data[:generated_at]]
    csv << [] # Empty row

    data = report_data[:data]

    if daily_data?(data)
      generate_daily_csv(csv, data)
    else
      generate_summary_csv(csv, data)
    end
  end

  def daily_data?(data)
    data.is_a?(Hash) && data.keys.first&.match?(/\d{4}-\d{2}-\d{2}/)
  end

  def generate_daily_csv(csv, data)
    csv << ['Date', metric_name]
    
    data.each do |date, value|
      csv << [date, value]
    end

    # Add summary
    csv << []
    csv << ['Summary']
    csv << ['Total Days', data.keys.count]
    csv << ['Total', data.values.sum]
    csv << ['Average', (data.values.sum.to_f / data.keys.count).round(2)]
    csv << ['Maximum', data.values.max]
    csv << ['Minimum', data.values.min]
  end

  def generate_summary_csv(csv, data)
    csv << ['Metric', 'Value']
    
    data.each do |key, value|
      csv << [key.to_s.humanize, value]
    end
  end

  def generate_daily_comparison_csv(csv, period1_data, period2_data, comparison_data)
    # Get all unique dates from both periods
    all_dates = (period1_data.keys + period2_data.keys).uniq.sort

    csv << ['Date', "Period 1 #{metric_name}", "Period 2 #{metric_name}", 'Change', '% Change']
    
    all_dates.each do |date|
      value1 = period1_data[date] || 0
      value2 = period2_data[date] || 0
      change = value2 - value1
      percentage_change = value1 > 0 ? ((change.to_f / value1) * 100).round(2) : 0
      
      csv << [date, value1, value2, change, "#{percentage_change}%"]
    end

    # Add summary comparison
    csv << []
    csv << ['Summary Comparison']
    csv << ['Metric', 'Period 1', 'Period 2', 'Change', '% Change']
    csv << ['Total', comparison_data[:period1_total], comparison_data[:period2_total], 
            comparison_data[:change], "#{comparison_data[:percentage_change]}%"]
  end

  def generate_summary_comparison_csv(csv, period1_data, period2_data, comparison_data)
    csv << ['Metric', 'Period 1', 'Period 2', 'Change', '% Change']
    
    comparison_data.each do |key, values|
      next unless values.is_a?(Hash)
      
      csv << [
        key.to_s.humanize,
        values[:period1],
        values[:period2],
        values[:change],
        "#{values[:percentage_change]}%"
      ]
    end
  end

  def metric_name
    case report_type
    when 'daily_logged_in_users'
      'Logged In Users'
    when 'daily_lead_creation'
      'Leads Created'
    when 'daily_deal_creation'
      'Deals Created'
    when 'daily_contact_creation'
      'Contacts Created'
    when 'daily_task_creation'
      'Tasks Created'
    when 'daily_meeting_creation'
      'Meetings Created'
    when 'daily_note_creation'
      'Notes Created'
    when 'daily_company_creation'
      'Companies Created'
    when 'daily_quote_creation'
      'Quotes Created'
    when 'daily_calls_logged'
      'Calls Logged'
    when 'daily_emails_sent'
      'Emails Sent'
    else
      'Value'
    end
  end
end
