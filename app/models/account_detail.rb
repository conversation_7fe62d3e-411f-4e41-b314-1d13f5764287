# frozen_string_literal: true

class AccountDetail < ApplicationRecord
  validates :email, presence: true, format: { with: EMAIL_REGEX }
  validates :mobile, format: { with: MOBILE_NUMBER_REGEX }, allow_blank: true

  belongs_to :tenant
  has_one :account_manager, class_name: 'User'
  has_one :support_executive, class_name: 'User'

  # Serialize marketplace_apps_installed as JSON array
  serialize :marketplace_apps_installed, Array
end
