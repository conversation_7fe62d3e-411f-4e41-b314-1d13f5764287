<div class="sidenav">
  <div class="btn-grp">
    <div class="<%= nav_link_class(default_class: 'nav-btn', active_class: 'nav-btn active', condition: false) %>" >
      <svg id="Icon_Dashboard_Default" data-name="Icon/Dashboard/Default" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="margin: 10px">
        <rect id="Icon_Plate" data-name="Icon/Plate" width="24" height="24" fill="none"/>
        <path id="Icon_Dashboard" data-name="Icon/Dashboard" d="M-285,2474a1,1,0,0,1-1-1v-7a1,1,0,0,1,1-1h5a1,1,0,0,1,1,1v7a1,1,0,0,1-1,1Zm9,0a1,1,0,0,1-1-1v-3a1,1,0,0,1,1-1h5a1,1,0,0,1,1,1v3a1,1,0,0,1-1,1Zm0-7a1,1,0,0,1-1-1v-7a1,1,0,0,1,1-1h5a1,1,0,0,1,1,1v7a1,1,0,0,1-1,1Zm-9-4a1,1,0,0,1-1-1v-3a1,1,0,0,1,1-1h5a1,1,0,0,1,1,1v3a1,1,0,0,1-1,1Z" transform="translate(290 -2454)" fill="#e5f0fe"/>
      </svg>
      <a class="btn-name" href=<%= root_path %>>
        <%= t('app.sidenav.dashboard') %>
      </a>
    </div>
    <div class="<%= nav_link_class(default_class: 'nav-btn', active_class: 'nav-btn active', condition: ACCOUNTS_CONTROLLERS.include?(params[:controller])) %>" >
      <svg id="Icon_Account_Default" data-name="Icon/Account/Default" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="margin: 10px">
        <rect id="Icon_Plate" data-name="Icon/Plate" width="24" height="24" fill="none"/>
        <path id="Icon_Account" data-name="Icon/Account" d="M-1868.667,92.777A1.334,1.334,0,0,1-1870,91.445V83h10v8.445a1.333,1.333,0,0,1-1.333,1.333Zm.53-2.327a.439.439,0,0,0,.434.55h5.183a.438.438,0,0,0,.434-.55,1.782,1.782,0,0,0-1.692-1.228h-.228a2.874,2.874,0,0,1-1.106.222,2.885,2.885,0,0,1-1.105-.222h-.228A1.78,1.78,0,0,0-1868.136,90.45Zm1.247-3.894a1.779,1.779,0,0,0,1.777,1.777,1.78,1.78,0,0,0,1.778-1.777,1.779,1.779,0,0,0-1.778-1.777A1.779,1.779,0,0,0-1866.889,86.556Zm9.169,6.215a1.26,1.26,0,0,1-1.252-1.326V89h5.036v2.444a1.4,1.4,0,0,1-1.415,1.326ZM-1858.8,88a.2.2,0,0,1-.2-.2V86.2a.2.2,0,0,1,.2-.2h4.6a.2.2,0,0,1,.2.2v1.6a.2.2,0,0,1-.2.2Zm0-3a.2.2,0,0,1-.2-.2V83.2a.2.2,0,0,1,.2-.2h4.6a.2.2,0,0,1,.2.2v1.6a.2.2,0,0,1-.2.2Zm-11.172-3.012v-.445a1.505,1.505,0,0,1,1.3-1.543h13.334a1.553,1.553,0,0,1,1.362,1.543v.445Z" transform="translate(1874 -74)" fill="#e5f0fe"/>
      </svg>
      <a class="btn-name" href=<%= account_details_path %>>
        <%= t('app.sidenav.accounts') %>
      </a>
    </div>
    <div class="nav-btn" >
      <svg id="Icon_Reports_Default" data-name="Icon/Reports/Default" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="margin: 10px">
        <rect id="Icon_Plate" data-name="Icon/Plate" width="24" height="24" fill="none"/>
        <g id="Icon_Report" data-name="Icon/Report" transform="translate(-3 -3)">
          <g id="Group_12497" data-name="Group 12497" transform="translate(6 6)">
            <path id="Path_12983" data-name="Path 12983" d="M2.551,8.438a1.574,1.574,0,0,0,1.1-.445l1.561.78a1.549,1.549,0,0,0-.019.192,1.582,1.582,0,0,0,3.164,0,1.563,1.563,0,0,0-.159-.678L10.311,6.17a1.563,1.563,0,0,0,.678.159A1.584,1.584,0,0,0,12.57,4.746a1.558,1.558,0,0,0-.079-.469l1.84-1.38a1.581,1.581,0,1,0-.706-1.316,1.558,1.558,0,0,0,.079.469l-1.84,1.38a1.575,1.575,0,0,0-2.3,1.994L7.447,7.541a1.555,1.555,0,0,0-1.773.286l-1.561-.78a1.549,1.549,0,0,0,.019-.192A1.582,1.582,0,1,0,2.551,8.438Zm0,0" transform="translate(0.121 0)" fill="#e5f0fe"/>
            <path id="Path_12984" data-name="Path 12984" d="M17.473,16.359H16.91V5.215a.527.527,0,0,0-.527-.527H14.273a.527.527,0,0,0-.527.527V16.359H12.691V8.379a.527.527,0,0,0-.527-.527H10.055a.527.527,0,0,0-.527.527v7.98H8.473V12.6a.527.527,0,0,0-.527-.527H5.836a.527.527,0,0,0-.527.527v3.762H4.254V10.488a.527.527,0,0,0-.527-.527H1.617a.527.527,0,0,0-.527.527v5.871H.527a.527.527,0,1,0,0,1.055H17.473a.527.527,0,0,0,0-1.055Zm0,0" transform="translate(0 0.586)" fill="#e5f0fe"/>
          </g>
        </g>
      </svg>
      <a class="btn-name" >
        <%= t('app.sidenav.reports') %>
      </a>
    </div>
    <% if current_user.role == ADMIN %>
      <div class="<%= nav_link_class(default_class: 'nav-btn', active_class: 'nav-btn active', condition: params[:controller] == "users") %>" >
        <i class="fa-solid fa-users-gear" style="margin:10px"></i>
        <a class="btn-name" href=<%= users_path %> >
          <%= t('app.sidenav.manage_users') %>
        </a>
      </div>
    <% end %>  
  </div>
</div>
