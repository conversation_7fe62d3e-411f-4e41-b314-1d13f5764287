<% period1_data = report_data[:period1][:data] %>
<% period2_data = report_data[:period2][:data] %>
<% comparison = report_data[:comparison] %>

<!-- Period Information -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header bg-primary text-white">
        <h6 class="mb-0"><%= t('usage_reports.period_1') %></h6>
      </div>
      <div class="card-body">
        <p class="mb-0">
          <%= report_data[:period1][:start].strftime('%B %d, %Y') %> - 
          <%= report_data[:period1][:end].strftime('%B %d, %Y') %>
        </p>
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-header bg-success text-white">
        <h6 class="mb-0"><%= t('usage_reports.period_2') %></h6>
      </div>
      <div class="card-body">
        <p class="mb-0">
          <%= report_data[:period2][:start].strftime('%B %d, %Y') %> - 
          <%= report_data[:period2][:end].strftime('%B %d, %Y') %>
        </p>
      </div>
    </div>
  </div>
</div>

<% if period1_data.is_a?(Hash) && period1_data.keys.first&.match?(/\d{4}-\d{2}-\d{2}/) %>
  <!-- Daily Data Comparison -->
  <div class="daily-comparison">
    <!-- Chart Visualization -->
    <div class="chart-container">
      <canvas id="comparisonChart"></canvas>
    </div>

    <!-- Summary Comparison -->
    <div class="row mt-4">
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value text-primary"><%= number_with_delimiter(comparison[:period1_total]) %></div>
          <div class="metric-label"><%= t('usage_reports.period_1_total') %></div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value text-success"><%= number_with_delimiter(comparison[:period2_total]) %></div>
          <div class="metric-label"><%= t('usage_reports.period_2_total') %></div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value <%= comparison[:change] >= 0 ? 'text-success' : 'text-danger' %>">
            <%= comparison[:change] >= 0 ? '+' : '' %><%= number_with_delimiter(comparison[:change]) %>
          </div>
          <div class="metric-label"><%= t('usage_reports.change') %></div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value <%= comparison[:percentage_change] >= 0 ? 'text-success' : 'text-danger' %>">
            <%= comparison[:percentage_change] >= 0 ? '+' : '' %><%= comparison[:percentage_change] %>%
          </div>
          <div class="metric-label"><%= t('usage_reports.percentage_change') %></div>
        </div>
      </div>
    </div>

    <!-- Detailed Comparison Table -->
    <div class="table-responsive mt-4">
      <table class="table table-striped">
        <thead>
          <tr>
            <th><%= t('usage_reports.date') %></th>
            <th class="text-primary"><%= t('usage_reports.period_1') %></th>
            <th class="text-success"><%= t('usage_reports.period_2') %></th>
            <th><%= t('usage_reports.change') %></th>
            <th><%= t('usage_reports.percentage_change') %></th>
          </tr>
        </thead>
        <tbody>
          <% all_dates = (period1_data.keys + period2_data.keys).uniq.sort %>
          <% all_dates.each do |date| %>
            <% value1 = period1_data[date] || 0 %>
            <% value2 = period2_data[date] || 0 %>
            <% change = value2 - value1 %>
            <% percentage_change = value1 > 0 ? ((change.to_f / value1) * 100).round(2) : 0 %>
            <tr>
              <td><%= Date.parse(date).strftime('%B %d, %Y') %></td>
              <td class="text-primary"><%= number_with_delimiter(value1) %></td>
              <td class="text-success"><%= number_with_delimiter(value2) %></td>
              <td class="<%= change >= 0 ? 'text-success' : 'text-danger' %>">
                <%= change >= 0 ? '+' : '' %><%= number_with_delimiter(change) %>
              </td>
              <td class="<%= percentage_change >= 0 ? 'text-success' : 'text-danger' %>">
                <%= percentage_change >= 0 ? '+' : '' %><%= percentage_change %>%
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    (function() {
      const ctx = document.getElementById('comparisonChart');
      if (ctx) {
        const period1Data = <%= raw period1_data.to_json %>;
        const period2Data = <%= raw period2_data.to_json %>;

        const allDates = [...new Set([...Object.keys(period1Data), ...Object.keys(period2Data)])].sort();

        new Chart(ctx.getContext('2d'), {
          type: 'line',
          data: {
            labels: allDates,
            datasets: [{
              label: '<%= t("usage_reports.period_1") %>',
              data: allDates.map(date => period1Data[date] || 0),
              borderColor: 'rgb(54, 162, 235)',
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              tension: 0.1
            }, {
              label: '<%= t("usage_reports.period_2") %>',
              data: allDates.map(date => period2Data[date] || 0),
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              tension: 0.1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            },
            plugins: {
              title: {
                display: true,
                text: '<%= t("usage_reports.types.#{report_data[:report_type]}") %> - <%= t("usage_reports.comparison") %>'
              }
            }
          }
        });
      }
    })();
  </script>

<% else %>
  <!-- Summary Data Comparison -->
  <div class="summary-comparison">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th><%= t('usage_reports.metric') %></th>
            <th class="text-primary"><%= t('usage_reports.period_1') %></th>
            <th class="text-success"><%= t('usage_reports.period_2') %></th>
            <th><%= t('usage_reports.change') %></th>
            <th><%= t('usage_reports.percentage_change') %></th>
          </tr>
        </thead>
        <tbody>
          <% comparison.each do |key, values| %>
            <% next unless values.is_a?(Hash) %>
            <tr>
              <td><%= key.to_s.humanize %></td>
              <td class="text-primary"><%= number_with_delimiter(values[:period1]) %></td>
              <td class="text-success"><%= number_with_delimiter(values[:period2]) %></td>
              <td class="<%= values[:change] >= 0 ? 'text-success' : 'text-danger' %>">
                <%= values[:change] >= 0 ? '+' : '' %><%= number_with_delimiter(values[:change]) %>
              </td>
              <td class="<%= values[:percentage_change] >= 0 ? 'text-success' : 'text-danger' %>">
                <%= values[:percentage_change] >= 0 ? '+' : '' %><%= values[:percentage_change] %>%
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Summary Cards -->
    <div class="metrics-grid mt-4">
      <% comparison.each do |key, values| %>
        <% next unless values.is_a?(Hash) %>
        <div class="metric-card">
          <div class="metric-value"><%= key.to_s.humanize %></div>
          <div class="metric-label">
            <span class="text-primary"><%= number_with_delimiter(values[:period1]) %></span>
            →
            <span class="text-success"><%= number_with_delimiter(values[:period2]) %></span>
          </div>
          <div class="comparison-indicator <%= values[:change] >= 0 ? 'comparison-positive' : 'comparison-negative' %>">
            <%= values[:change] >= 0 ? '↗' : '↘' %>
            <%= values[:change] >= 0 ? '+' : '' %><%= values[:percentage_change] %>%
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
