<% data = report_data[:data] %>

<% if data.is_a?(Hash) && data.keys.first&.match?(/\d{4}-\d{2}-\d{2}/) %>
  <!-- Daily Data Report -->
  <div class="daily-report">
    <!-- Chart Visualization -->
    <div class="chart-container">
      <canvas id="dailyChart"></canvas>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value"><%= data.values.sum %></div>
          <div class="metric-label"><%= t('usage_reports.total') %></div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value"><%= (data.values.sum.to_f / data.keys.count).round(2) %></div>
          <div class="metric-label"><%= t('usage_reports.average_per_day') %></div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value"><%= data.values.max %></div>
          <div class="metric-label"><%= t('usage_reports.maximum') %></div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="metric-card">
          <div class="metric-value"><%= data.values.min %></div>
          <div class="metric-label"><%= t('usage_reports.minimum') %></div>
        </div>
      </div>
    </div>

    <!-- Detailed Table -->
    <div class="table-responsive mt-4">
      <table class="table table-striped">
        <thead>
          <tr>
            <th><%= t('usage_reports.date') %></th>
            <th><%= t("usage_reports.metric_names.#{report_data[:report_type]}") %></th>
          </tr>
        </thead>
        <tbody>
          <% data.sort.each do |date, value| %>
            <tr>
              <td><%= Date.parse(date).strftime('%B %d, %Y') %></td>
              <td><%= number_with_delimiter(value) %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    (function() {
      const ctx = document.getElementById('dailyChart');
      if (ctx) {
        const chartData = <%= raw data.to_json %>;

        new Chart(ctx.getContext('2d'), {
          type: 'line',
          data: {
            labels: Object.keys(chartData).sort(),
            datasets: [{
              label: '<%= t("usage_reports.metric_names.#{report_data[:report_type]}") %>',
              data: Object.keys(chartData).sort().map(date => chartData[date]),
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              tension: 0.1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            },
            plugins: {
              title: {
                display: true,
                text: '<%= t("usage_reports.types.#{report_data[:report_type]}") %>'
              }
            }
          }
        });
      }
    })();
  </script>

<% else %>
  <!-- Summary Data Report -->
  <div class="summary-report">
    <div class="metrics-grid">
      <% data.each do |key, value| %>
        <div class="metric-card">
          <div class="metric-value"><%= number_with_delimiter(value) %></div>
          <div class="metric-label"><%= key.to_s.humanize %></div>
        </div>
      <% end %>
    </div>

    <!-- Detailed Table -->
    <div class="table-responsive mt-4">
      <table class="table table-striped">
        <thead>
          <tr>
            <th><%= t('usage_reports.metric') %></th>
            <th><%= t('usage_reports.value') %></th>
          </tr>
        </thead>
        <tbody>
          <% data.each do |key, value| %>
            <tr>
              <td><%= key.to_s.humanize %></td>
              <td><%= number_with_delimiter(value) %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
<% end %>
