<div class="report-data-container">
  <% if report_data[:error] %>
    <div class="alert alert-danger">
      <%= report_data[:error] %>
    </div>
  <% else %>
    <!-- Report Header -->
    <div class="report-header mb-4">
      <div class="row">
        <div class="col-md-8">
          <h4><%= t("usage_reports.types.#{report_data[:report_type]}") %></h4>
          <p class="text-muted">
            <% if report_data[:comparison_type] == 'none' %>
              <%= t('usage_reports.period_from_to', 
                    start: report_data[:period][:start], 
                    end: report_data[:period][:end]) %>
            <% else %>
              <%= t('usage_reports.comparison_report') %>
            <% end %>
          </p>
        </div>
        <div class="col-md-4 text-end">
          <small class="text-muted">
            <%= t('usage_reports.generated_at') %>: <%= report_data[:generated_at].strftime('%Y-%m-%d %H:%M') %>
          </small>
        </div>
      </div>
    </div>

    <% if report_data[:comparison_type] != 'none' %>
      <!-- Comparison Report -->
      <%= render 'comparison_report', report_data: report_data %>
    <% else %>
      <!-- Single Period Report -->
      <%= render 'single_period_report', report_data: report_data %>
    <% end %>
  <% end %>
</div>
