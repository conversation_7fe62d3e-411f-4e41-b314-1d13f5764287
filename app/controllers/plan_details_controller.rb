# frozen_string_literal: true

class PlanDetailsController < ApplicationController
  include PlanDetailsHelper

  before_action :authenticate_user!, :authenticate_status
  before_action :set_tenant_and_account_detail, only: %i[show update]

  def show
    if @account_detail.subscription_id.nil?
      flash[:danger] = I18n.t('danger.plan_detail_not_exists')
      redirect_to account_detail_path(@account_detail)
    else
      result = Chargebee::Subscription.get(@account_detail.subscription_id) if @account_detail.subscription_id
      if result.nil?
        flash[:danger] = t('danger.plan_detail_not_exists')
        redirect_to account_detail_path(@account_detail)
      else
        parse_subscription_detail(result)
      end
    end
  end

  private

  def plan_detail_params
    params.require(:plan_detail).permit(:ob_completion)
  end

  def set_tenant_and_account_detail
    @account_detail = AccountDetail.find_by(id: params[:id])
  end


  def authenticate_status
    if current_user.deactivated
      flash[:danger] = I18n.t('devise.failure.no_permission')
      sign_out(current_user)
      redirect_to new_user_session_path
    end
  end

  def parse_subscription_detail(response)
    @add_on = []
    response.dig('subscription', 'subscription_items').map do |item|
      if item.dig('item_type') == 'addon'
        @add_on.push({
          name: item.dig('item_price_id'),
          quantity: item.dig('quantity'),
          unit_price: item.dig('unit_price'),
          amount: item.dig('amount')
        })
      end
      if item.dig('item_type') == 'plan'
        @plan_name = item.dig('item_price_id')
        @plan_price = item.dig('amount')
      end
    end

    @mrr = response.dig('subscription', 'mrr').to_i / 100

    @plan_status = response.dig('subscription', 'status').titleize
    @next_renewal = Time.at(response.dig('subscription', 'next_billing_at') || response.dig('subscription', 'trial_end'))&.to_date
    @currency_code = response.dig('subscription', 'currency_code')
    @billing_period_unit = response.dig('subscription', 'billing_period_unit')
  end
end
