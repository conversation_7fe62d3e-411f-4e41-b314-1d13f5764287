# frozen_string_literal: true

class UsageReportsController < ApplicationController
  include Pagy::Backend

  before_action :authenticate_user!, :authenticate_status
  before_action :set_tenant_and_account_detail, only: %i[show data export]

  def show
    @report_types = report_types_options
    @comparison_types = comparison_types_options
    @date_ranges = date_range_options
  end

  def data
    report_service = UsageReportService.new(
      tenant: @tenant,
      report_type: params[:report_type],
      comparison_type: params[:comparison_type],
      date_range: params[:date_range],
      start_date: params[:start_date],
      end_date: params[:end_date]
    )

    @report_data = report_service.generate_report
    
    respond_to do |format|
      format.json { render json: @report_data }
      format.html { render partial: 'report_data', locals: { report_data: @report_data } }
    end
  rescue StandardError => e
    Rails.logger.error "Usage report generation failed: #{e.message}"
    respond_to do |format|
      format.json { render json: { error: 'Failed to generate report' }, status: :unprocessable_entity }
      format.html { render plain: 'Error generating report', status: :unprocessable_entity }
    end
  end

  def export
    report_service = UsageReportService.new(
      tenant: @tenant,
      report_type: params[:report_type],
      comparison_type: params[:comparison_type],
      date_range: params[:date_range],
      start_date: params[:start_date],
      end_date: params[:end_date]
    )

    @report_data = report_service.generate_report
    
    respond_to do |format|
      format.csv do
        csv_data = UsageReportExportService.new(@report_data, params[:report_type]).to_csv
        send_data csv_data, 
                  filename: "usage_report_#{@tenant.kylas_tenant_id}_#{Date.current}.csv",
                  type: 'text/csv'
      end
      format.json { render json: @report_data }
    end
  rescue StandardError => e
    Rails.logger.error "Usage report export failed: #{e.message}"
    redirect_to usage_reports_account_detail_path, alert: 'Failed to export report'
  end

  private

  def set_tenant_and_account_detail
    @account_detail = AccountDetail.find_by(id: params[:id])
    unless @account_detail
      redirect_to account_details_path, notice: t('account_details.not_found')
      return
    end
    @tenant = @account_detail.tenant
  end

  def authenticate_status
    if current_user.deactivated
      flash[:danger] = I18n.t('devise.failure.no_permission')
      sign_out(current_user)
      redirect_to new_user_session_path
    end
  end

  def report_types_options
    [
      ['Daily Logged In Users', 'daily_logged_in_users'],
      ['Daily Lead Creation', 'daily_lead_creation'],
      ['Daily Deal Creation', 'daily_deal_creation'],
      ['Daily Contact Creation', 'daily_contact_creation'],
      ['Daily Task Creation', 'daily_task_creation'],
      ['Daily Meeting Creation', 'daily_meeting_creation'],
      ['Daily Note Creation', 'daily_note_creation'],
      ['Daily Company Creation', 'daily_company_creation'],
      ['Daily Quote Creation', 'daily_quote_creation'],
      ['Daily Calls Logged', 'daily_calls_logged'],
      ['Daily Emails Sent', 'daily_emails_sent'],
      ['User Activity Summary', 'user_activity_summary'],
      ['Feature Usage Overview', 'feature_usage_overview'],
      ['Active vs Inactive Users', 'active_inactive_users'],
      ['Email Integration Usage', 'email_integration_usage'],
      ['Calendar Integration Usage', 'calendar_integration_usage'],
      ['Marketplace Apps Usage', 'marketplace_apps_usage']
    ]
  end

  def comparison_types_options
    [
      ['No Comparison', 'none'],
      ['Yesterday vs Today', 'yesterday_today'],
      ['Last Week vs This Week', 'last_week_this_week'],
      ['Last Month vs This Month', 'last_month_this_month'],
      ['Last Quarter vs This Quarter', 'last_quarter_this_quarter'],
      ['Last Year vs This Year', 'last_year_this_year'],
      ['Custom Date Range', 'custom']
    ]
  end

  def date_range_options
    [
      ['Last 7 Days', '7_days'],
      ['Last 30 Days', '30_days'],
      ['Last 90 Days', '90_days'],
      ['Last 6 Months', '6_months'],
      ['Last Year', '1_year'],
      ['Custom Range', 'custom']
    ]
  end
end
