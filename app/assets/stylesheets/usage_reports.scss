// Usage Reports Styling

.usage-reports-container {
  padding: 20px 0;

  .header-section {
    margin-bottom: 30px;
    
    h3 {
      color: #2c3e50;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    .text-muted {
      font-size: 0.95rem;
    }
  }

  .report-config-section {
    margin-bottom: 30px;
    
    .card {
      border: 1px solid #e3e6f0;
      border-radius: 8px;
      box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      
      .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        
        h5 {
          margin: 0;
          color: #5a5c69;
          font-weight: 600;
        }
      }
      
      .card-body {
        padding: 1.5rem;
      }
    }
    
    .form-label {
      font-weight: 600;
      color: #5a5c69;
      margin-bottom: 0.5rem;
    }
    
    .form-select, .form-control {
      border: 1px solid #d1d3e2;
      border-radius: 0.35rem;
      
      &:focus {
        border-color: #bac8f3;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
      }
    }
    
    .btn-group {
      .btn {
        margin-right: 0.5rem;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .report-results-section {
    margin-top: 30px;
    
    .report-data-container {
      .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 8px 8px 0 0;
        margin-bottom: 0;
        
        h4 {
          margin: 0;
          font-weight: 600;
        }
        
        .text-muted {
          color: rgba(255, 255, 255, 0.8) !important;
        }
      }
    }
  }

  .chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
    padding: 20px;
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
  }

  .metric-card {
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #5a5c69;
      margin-bottom: 0.5rem;
      
      &.text-primary {
        color: #4e73df !important;
      }
      
      &.text-success {
        color: #1cc88a !important;
      }
      
      &.text-danger {
        color: #e74a3b !important;
      }
    }
    
    .metric-label {
      font-size: 0.9rem;
      color: #858796;
      margin-top: 5px;
      font-weight: 600;
    }
    
    .comparison-indicator {
      font-size: 0.8rem;
      margin-top: 8px;
      font-weight: 600;
      
      &.comparison-positive {
        color: #1cc88a;
      }
      
      &.comparison-negative {
        color: #e74a3b;
      }
      
      &.comparison-neutral {
        color: #858796;
      }
    }
  }

  .table-responsive {
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    
    .table {
      margin-bottom: 0;
      
      thead th {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        color: #5a5c69;
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      tbody tr {
        &:hover {
          background-color: #f8f9fc;
        }
      }
      
      td {
        border-top: 1px solid #e3e6f0;
        color: #5a5c69;
        font-size: 0.9rem;
        
        &.text-primary {
          color: #4e73df !important;
          font-weight: 600;
        }
        
        &.text-success {
          color: #1cc88a !important;
          font-weight: 600;
        }
        
        &.text-danger {
          color: #e74a3b !important;
          font-weight: 600;
        }
      }
    }
  }

  .daily-comparison, .summary-comparison {
    .card {
      border: 1px solid #e3e6f0;
      border-radius: 8px;
      
      .card-header {
        font-weight: 600;
        
        &.bg-primary {
          background-color: #4e73df !important;
        }
        
        &.bg-success {
          background-color: #1cc88a !important;
        }
      }
    }
  }

  #loading-indicator {
    padding: 3rem 0;
    
    .spinner-border {
      width: 3rem;
      height: 3rem;
    }
    
    p {
      margin-top: 1rem;
      color: #858796;
      font-weight: 600;
    }
  }

  .alert {
    border-radius: 8px;
    border: none;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: 1fr;
    }
    
    .chart-container {
      height: 300px;
      padding: 15px;
    }
    
    .metric-card {
      padding: 1rem;
      
      .metric-value {
        font-size: 1.5rem;
      }
    }
  }
}

// Button styling improvements
.btn {
  border-radius: 0.35rem;
  font-weight: 600;
  
  &.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
    
    &:hover {
      background-color: #2e59d9;
      border-color: #2e59d9;
    }
  }
  
  &.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
    
    &:hover {
      background-color: #17a673;
      border-color: #17a673;
    }
  }
  
  &.btn-secondary {
    background-color: #858796;
    border-color: #858796;
    
    &:hover {
      background-color: #717384;
      border-color: #717384;
    }
  }
}
