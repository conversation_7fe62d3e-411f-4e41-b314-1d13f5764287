@import 'bootstrap-5.0.2.min';
@import 'dashboard';
@import 'authentication';
@import 'pre-login';
@import 'mailer';
@import 'navbar';
@import 'sidenav';
@import 'account-list';
@import 'account-details-layout';
@import 'plan_detail';
@import 'rgs_input';
@import 'customer_ask';
@import 'usage_reports';

body {
  font-family: 'Rubik', sans-serif;
  position:fixed;
  padding:0;
  margin:0;
  top:0;
  left:0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: $bg-color;
  .content{
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 90%;
    .main-container{
      display: flex;
      flex-direction: column;
      padding: 10px;
      width: 85%;
      height: 100%;
      overflow-y: auto;
    }
  }
}

.pgn-grp{
  display: flex;
  .pgn-container{
    display: flex;
    .pgn{
      background-color: $nav-bg-color;
      color: $text-color;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 36px;
      height:36px;
      font-size: 16px;
      border: 1px solid $dividers;
    }
    .end {
      width: 72px;
    }
    .active {
      background-color: $primary;
      color: $nav-bg-color;
    }
  }
  .results-input-container{
    display: flex;
    justify-content: center;
    align-items: center;
    color: $secondary-text-color;
    margin-inline: 10px;
    .select-pages{
      height: 100%;
      font-size: 17px;
      background-color: $nav-bg-color;
      color: $text-color;
      border: 1px solid $dividers;
      width: auto;
    }
  }
}

.btn-close {
  cursor: pointer;
}

.form-control:focus {
  color: #2e384d;
  background-color: #fff;
  border-color: #0176FF;
  outline: 0;
  box-shadow: none;
}

.form-control--password {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.toggle-password {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.password-box {
  flex: auto;
  width: min-content;
}

.custom_container{
  display: flex;
  flex-direction: column;
  padding: 30px;
}
.flash{
  border: none;
  border-radius: 5px;
  display: flex;
  align-items: center;
  background-color: $nav-bg-color;
  position: fixed;
  bottom: 10px;
  left: 10px;
  z-index: 9999;
  min-width: 350px;
  i{
    margin-right: 10px;
    width: 30px;
  }
}
