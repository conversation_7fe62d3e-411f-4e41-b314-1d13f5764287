version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:10
    container_name: kylas_customer_success_postgres
    environment:
      POSTGRES_DB: kylas_customer_success_development
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - kylas_network

  # PostgreSQL Test Database
  postgres_test:
    image: postgres:10
    container_name: kylas_customer_success_postgres_test
    environment:
      POSTGRES_DB: kylas_customer_success_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - kylas_network

  # Rails Application
  web:
    build: .
    container_name: kylas_customer_success_web
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -p 3000 -b '0.0.0.0'"
    volumes:
      - .:/app
      - bundle_cache:/usr/local/bundle
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: password
      TEST_DATABASE_HOST: postgres_test
      TEST_DATABASE_PORT: 5432
      RAILS_ENV: development
    networks:
      - kylas_network

volumes:
  postgres_data:
  postgres_test_data:
  bundle_cache:

networks:
  kylas_network:
    driver: bridge
