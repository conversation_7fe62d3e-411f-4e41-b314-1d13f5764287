-- Initialize PostgreSQL for Kylas Customer Success Portal
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions that might be useful
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set timezone
SET timezone = 'UTC';

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE kylas_customer_success_development TO postgres;

-- Log initialization
SELECT 'PostgreSQL initialized for Kylas Customer Success Portal' AS status;
