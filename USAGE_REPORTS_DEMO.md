# Usage Reports Demo Guide

This guide will help you set up and test the comprehensive Usage Reports feature with realistic sample data.

## 🚀 Quick Start

### 1. Generate Sample Data

Run the following command to create comprehensive sample data:

```bash
rake usage_reports:demo_data
```

This will create:
- **3 sample tenants** with different characteristics
- **90 days of usage history** with realistic patterns
- **Multiple user types** (active, power users, inactive)
- **Seasonal patterns** (weekends vs weekdays)
- **Growth trends** over time
- **Customer requirements** and **RGS inputs**

### 2. Start the Application

```bash
rails server
```

### 3. Access Usage Reports

1. Login to the application
2. Navigate to any account detail page
3. Click on the **"Usage Reports"** tab
4. Start exploring different report types!

## 📊 Sample Tenants Created

### 1. TechCorp Solutions (ID: 1000)
- **Industry**: Technology
- **Users**: 15 (12 active, 3 inactive)
- **Characteristics**: High activity, tech-savvy users
- **Apps**: <PERSON><PERSON><PERSON> SMS, Email Templates

### 2. Global Innovations Inc (ID: 1001)
- **Industry**: Manufacturing
- **Users**: 25 (20 active, 5 inactive)
- **Characteristics**: Medium activity, process-oriented
- **Apps**: Kylas SMS, Custom Fields, Workflows

### 3. Digital Dynamics LLC (ID: 1002)
- **Industry**: Consulting
- **Users**: 40 (35 active, 5 inactive)
- **Characteristics**: Very high activity, power users
- **Apps**: Email Templates, Custom Fields, Workflows, File Storage

## 🎯 Recommended Test Scenarios

### Scenario 1: Daily Activity Analysis
**Report Type**: Daily Logged In Users  
**Comparison**: Yesterday vs Today  
**Purpose**: See immediate login pattern changes

### Scenario 2: Lead Generation Trends
**Report Type**: Daily Lead Creation  
**Comparison**: Last Month vs This Month  
**Purpose**: Analyze lead generation performance

### Scenario 3: Quarterly Business Review
**Report Type**: User Activity Summary  
**Comparison**: Last Quarter vs This Quarter  
**Purpose**: Comprehensive quarterly analysis

### Scenario 4: Feature Adoption
**Report Type**: Feature Usage Overview  
**Comparison**: No Comparison  
**Date Range**: Last 90 Days  
**Purpose**: Understand which features are most used

### Scenario 5: Weekly Performance
**Report Type**: Daily Deal Creation  
**Comparison**: Last Week vs This Week  
**Purpose**: Track weekly deal creation patterns

### Scenario 6: App Usage Analysis
**Report Type**: Marketplace Apps Usage  
**Comparison**: Last Month vs This Month  
**Purpose**: Monitor app adoption and usage

## 📈 Data Patterns to Observe

### Realistic Patterns Built Into Sample Data:

1. **Weekend Effect**: Lower activity on weekends
2. **User Types**: 
   - 80% active users with regular activity
   - 30% power users with high activity
   - 20% inactive users with minimal activity
3. **Growth Trends**: Slight upward trend over 90 days
4. **Seasonal Variations**: Random fluctuations mimicking real usage
5. **Feature Usage**: Different features used at different rates

### Expected Insights:

- **Login Patterns**: Higher weekday activity
- **Lead Creation**: Varies by tenant size and industry
- **Feature Usage**: Power users drive most advanced feature usage
- **Integration Adoption**: Varies by user type and tenant
- **Growth Trends**: Gradual improvement over time

## 🔧 Advanced Testing

### Custom Date Ranges
Try these specific date ranges:
- **Last 7 days**: Recent activity patterns
- **Last 30 days**: Monthly trends
- **Last 90 days**: Quarterly analysis
- **Custom range**: Pick specific periods

### Export Testing
1. Generate any report
2. Click **"Export CSV"**
3. Open the downloaded file to see formatted data
4. Use in Excel/Google Sheets for further analysis

### Comparison Types
Test all comparison options:
- **Yesterday vs Today**: Daily changes
- **Last Week vs This Week**: Weekly trends
- **Last Month vs This Month**: Monthly growth
- **Last Quarter vs This Quarter**: Quarterly analysis
- **Custom**: Compare any two periods

## 🛠️ Management Commands

### View Statistics
```bash
rake usage_reports:stats
```
Shows overview of generated data.

### Clean Up Demo Data
```bash
rake usage_reports:clean_demo_data
```
Removes all sample data when you're done testing.

### Generate Additional Data
```bash
rake usage_reports:generate_data[2024-01-01,2024-01-31,1000]
```
Generate data for specific date range and tenant.

## 📊 Report Types Available

### Daily Activity Reports
- Daily Logged In Users
- Daily Lead Creation
- Daily Deal Creation
- Daily Contact Creation
- Daily Task Creation
- Daily Meeting Creation
- Daily Note Creation
- Daily Company Creation
- Daily Quote Creation
- Daily Calls Logged
- Daily Emails Sent

### Summary Reports
- User Activity Summary
- Feature Usage Overview
- Active vs Inactive Users
- Email Integration Usage
- Calendar Integration Usage
- Marketplace Apps Usage

## 🎨 UI Features to Test

### Interactive Elements
- **Dynamic Form**: Options change based on selections
- **Real-time Loading**: Progress indicators during generation
- **Responsive Charts**: Interactive Chart.js visualizations
- **Error Handling**: Try invalid combinations
- **Mobile View**: Test on different screen sizes

### Visual Elements
- **Color-coded Metrics**: Green for positive, red for negative
- **Trend Indicators**: Arrows showing direction of change
- **Summary Cards**: Key metrics at a glance
- **Detailed Tables**: Complete data breakdown

## 💡 Pro Tips

1. **Start Simple**: Begin with "Daily Logged In Users" and "No Comparison"
2. **Try Different Tenants**: Each has different patterns
3. **Use Comparisons**: Most insights come from comparing periods
4. **Export Data**: CSV exports are great for external analysis
5. **Mobile Testing**: Reports are fully responsive
6. **Custom Dates**: Use custom ranges for specific analysis periods

## 🐛 Troubleshooting

### No Data Showing
- Ensure sample data was generated: `rake usage_reports:stats`
- Check date ranges - data covers last 90 days
- Verify tenant has usage history records

### Performance Issues
- Large date ranges may take longer to process
- Try smaller date ranges first
- Check server logs for any errors

### Export Issues
- Ensure CSV format is supported by your browser
- Check download folder for exported files
- Try different report types if one fails

## 🎉 What's Next?

After testing with sample data:

1. **Integrate Real Data**: Connect to actual usage data sources
2. **Customize Reports**: Add new report types as needed
3. **Automate Exports**: Set up scheduled report generation
4. **Dashboard Integration**: Embed charts in dashboards
5. **User Training**: Train team members on report usage

## 📞 Support

If you encounter any issues:
1. Check the Rails logs for error details
2. Verify sample data exists with `rake usage_reports:stats`
3. Try regenerating data with `rake usage_reports:demo_data`
4. Clean and regenerate if needed

---

**Happy reporting! 📊✨**
